import { defineConfig, devices } from '@playwright/test';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define storage state path to store authentication information
export const STORAGE_STATE = path.join(__dirname, 'playwright/.auth/user.json');

export default defineConfig({
	testDir: './tests',
	fullyParallel: true,
	forbidOnly: !!process.env.CI,
	retries: process.env.CI ? 2 : 0,
	workers: process.env.CI ? 1 : undefined,
	reporter: 'html',
	timeout: 60000, // Sets default test timeout to 60 seconds

	// Setup project for authentication
	projects: [
		{
			name: 'setup',
			testMatch: /auth\.setup\.ts/
		},

		// Desktop Browser Projects - Login Tests
		{
			name: 'login-chromium',
			testMatch: /login.*\.test\.ts/,
			use: {
				...devices['Desktop Chrome']
				// No storage state - start unauthenticated for login tests
			}
		},

		// Authenticated Tests - Multi-browser
		{
			name: 'auth-chromium',
			testMatch: /auth-flow\.test\.ts/,
			dependencies: ['setup'],
			use: {
				...devices['Desktop Chrome'],
				storageState: STORAGE_STATE
			}
		},

		// Chat Center Tests
		{
			name: 'chat-center-chromium',
			testMatch: '**/chat-center-*.test.ts',
			use: {
				...devices['Desktop Chrome']
				// No storage state - each test handles its own authentication
			}
		},

		// Ticket Filtering Tests
		{
			name: 'ticket-chromium',
			testMatch: '**/ticket-*.test.ts',
			use: {
				...devices['Desktop Chrome']
				// No storage state - handles own authentication
			}
		},

		// Settings-Account Tests
		{
			name: 'settings-account-chromium',
			testMatch: '**/settings-account-*.test.ts',
			use: {
				...devices['Desktop Chrome']
				// No storage state - handles own authentication
			}
		},

		// Settings-Team Management Tests
		{
			name: 'settings-team-chromium',
			testMatch: '**/settings-team-*.test.ts',
			use: {
				...devices['Desktop Chrome']
				// No storage state - handles own authentication
			}
		},

		// Settings-General Tests
		{
			name: 'settings-general-chromium',
			testMatch: '**/settings-general-*.test.ts',
			use: {
				...devices['Desktop Chrome']
				// No storage state - handles own authentication
			}
		}
	],

	use: {
		baseURL: 'http://localhost:8000',
		trace: 'on-first-retry',
		screenshot: 'only-on-failure'
	},

	webServer: {
		command: 'npm run dev',
		port: 8000,
		reuseExistingServer: !process.env.CI
	}
});
