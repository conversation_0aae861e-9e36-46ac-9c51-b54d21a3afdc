<!-- ChatbotSection.svelte -->
<script lang="ts">
	import { onD<PERSON>roy, createEventDispatcher } from 'svelte';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { Button } from 'flowbite-svelte';
	import { CheckOutline } from 'flowbite-svelte-icons';
	import { t } from '$lib/stores/i18n';
	import { toastStore } from '$lib/stores/toastStore';

	const dispatch = createEventDispatcher();

	export let botSettings;

	// Options
	const genderOptions = ['Male', 'Female'];
	const conversationStyles = ['Formal', 'Casual', 'Professional', 'Friendly'];
	const rolesOptions = ['Customer Support'];
	const conversationTypes = [
		'Chat'
		// 'Calling'
	];

	// Track changes
	let originalValues = {};
	let changedFields = new Set();
	let hasUnsavedChanges = false;

	// Form reference
	let settingsForm;

	// Initialize original values when component loads
	$: {
		if (
			$botSettings &&
			Object.keys($botSettings).length > 0 &&
			Object.keys(originalValues).length === 0
		) {
			originalValues = { ...$botSettings };
		}
	}

	// Track field changes
	function trackChange(field) {
		if ($botSettings[field] !== originalValues[field]) {
			changedFields.add(field);
		} else {
			changedFields.delete(field);
		}
		hasUnsavedChanges = changedFields.size > 0;
	}

	// Enhance function for form submission
	function enhanceOptions() {
		return async ({ result, update }) => {
			if (result.type === 'error') {
				// Handle errors
				toastStore.add('Error saving changes', 'error');
			} else if (result.type === 'success') {
				// Invalidate all data to refresh from server
				await invalidateAll();
				
				// Dispatch event to parent component
				// dispatch('settings-updated');
				
				// Reset form state
				originalValues = { ...$botSettings };
				changedFields.clear();
				hasUnsavedChanges = false;
				
				// Show success toast
				toastStore.add('Changes saved successfully!', 'success');
			}
		};
	}

	// Handle form submission
	function handleSubmit() {
		// Any additional handling before form submission
	}

	// Save bot settings
	function saveSettings() {
		const settings = [
			// Chatbot settings
			{ key: 'CHATBOT_MASCOT_THAI_NAME', value: $botSettings.thaiName },
			{ key: 'CHATBOT_MASCOT_ENGLISH_NAME', value: $botSettings.englishName },
			{ key: 'CHATBOT_ROLE', value: $botSettings.role },
			{ key: 'CHATBOT_GENDER', value: $botSettings.gender },
			{ key: 'CHATBOT_CONVERSATION_STYLE', value: $botSettings.conversationStyle },
			{ key: 'CHATBOT_CONVERSATION_TYPE', value: $botSettings.conversationType }
		];

		const settingsInput = settingsForm.querySelector('input[name="settings"]');
		settingsInput.value = JSON.stringify(settings);

		settingsForm.requestSubmit();
	}

	function saveChanges() {
		saveSettings();
	}

	// Handle page navigation - reset to original values
	onDestroy(() => {
		if (hasUnsavedChanges) {
			$botSettings = { ...originalValues };
		}
	});
</script>

<div class="space-y-4 rounded-lg bg-white p-6 shadow-md">

	<div class="flex items-center justify-between">
		<div>
			<h2 class="text-xl font-medium text-gray-700">{t('chatbot_title')}</h2>
			<p class="text-sm text-gray-500">{t('chatbot_description')}</p>
		</div>

		<!-- Save changes button -->
		<Button
			id="chatbot-section-save-button"
			type="button"
			color="green"
			class="disabled:cursor-not-allowed disabled:opacity-20"
			on:click={saveChanges}
			disabled={!hasUnsavedChanges}
		>
			<!-- {#if hasUnsavedChanges}
				{t('save_changes')}
			{:else} -->
			<CheckOutline class="mr-2 h-4 w-4" />
			{t('save')}
			<!-- {/if} -->
		</Button>
	</div>

	<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
		<div>
			<label for="chatbot-section-thai-name-input" class="block text-sm font-medium text-gray-700">
				{t('chatbot_thai_name')}
				{#if changedFields.has('thaiName')}
					<span class="ml-1 text-blue-600">({t('modified')})</span>
				{/if}
			</label>
			<input
				type="text"
				id="chatbot-section-thai-name-input"
				class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500
                {changedFields.has('thaiName') ? 'border-blue-500 bg-blue-50' : ''}"
				bind:value={$botSettings.thaiName}
				on:input={() => trackChange('thaiName')}
			/>
		</div>

		<div>
			<label for="chatbot-section-english-name-input" class="block text-sm font-medium text-gray-700">
				{t('chatbot_english_name')}
				{#if changedFields.has('englishName')}
					<span class="ml-1 text-blue-600">({t('modified')})</span>
				{/if}
			</label>
			<input
				type="text"
				id="chatbot-section-english-name-input"
				class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500
                {changedFields.has('englishName') ? 'border-blue-500 bg-blue-50' : ''}"
				bind:value={$botSettings.englishName}
				on:input={() => trackChange('englishName')}
			/>
		</div>

		<div>
			<label for="chatbot-section-role-select" class="block text-sm font-medium text-gray-700">
				{t('chatbot_role')}
				{#if changedFields.has('role')}
					<span class="ml-1 text-blue-600">({t('modified')})</span>
				{/if}
			</label>
			<select
				id="chatbot-section-role-select"
				class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500
                {changedFields.has('role') ? 'border-blue-500 bg-blue-50' : ''}"
				bind:value={$botSettings.role}
				on:change={() => trackChange('role')}
			>
				<option value="">{t('select_role')}</option>
				{#each rolesOptions as role}
					<option value={role}>{role}</option>
				{/each}
			</select>
		</div>

		<div>
			<label for="chatbot-section-gender-select" class="block text-sm font-medium text-gray-700">
				{t('chatbot_gender')}
				{#if changedFields.has('gender')}
					<span class="ml-1 text-blue-600">({t('modified')})</span>
				{/if}
			</label>
			<select
				id="chatbot-section-gender-select"
				class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500
                {changedFields.has('gender') ? 'border-blue-500 bg-blue-50' : ''}"
				bind:value={$botSettings.gender}
				on:change={() => trackChange('gender')}
			>
				<option value="">{t('select_gender')}</option>
				{#each genderOptions as gender}
					<option value={gender}>{gender}</option>
				{/each}
			</select>
		</div>

		<div>
			<label for="chatbot-section-conversation-style-select" class="block text-sm font-medium text-gray-700">
				{t('chatbot_style')}
				{#if changedFields.has('conversationStyle')}
					<span class="ml-1 text-blue-600">({t('modified')})</span>
				{/if}
			</label>
			<select
				id="chatbot-section-conversation-style-select"
				class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500
                {changedFields.has('conversationStyle') ? 'border-blue-500 bg-blue-50' : ''}"
				bind:value={$botSettings.conversationStyle}
				on:change={() => trackChange('conversationStyle')}
			>
				<option value="">{t('select_style')}</option>
				{#each conversationStyles as type}
					<option value={type}>{type}</option>
				{/each}
			</select>
		</div>

		<div>
			<label for="chatbot-section-conversation-type-select" class="block text-sm font-medium text-gray-700">
				{t('chatbot_type')}
				{#if changedFields.has('conversationType')}
					<span class="ml-1 text-blue-600">({t('modified')})</span>
				{/if}
			</label>
			<select
				id="chatbot-section-conversation-type-select"
				class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500
                {changedFields.has('conversationType') ? 'border-blue-500 bg-blue-50' : ''}"
				bind:value={$botSettings.conversationType}
				on:change={() => trackChange('conversationType')}
			>
				<option value="">{t('select_type')}</option>
				{#each conversationTypes as type}
					<option value={type}>{type}</option>
				{/each}
			</select>
		</div>
	</div>

	<!-- Moved the warning notification to the bottom -->
	{#if hasUnsavedChanges}
		<div id="chatbot-section-unsaved-changes-warning" class="mt-6 border-l-4 border-amber-400 bg-amber-50 p-4">
			<div class="flex">
				<div class="flex-shrink-0">
					<svg
						class="h-5 w-5 text-amber-400"
						xmlns="http://www.w3.org/2000/svg"
						fill="currentColor"
						viewBox="0 0 20 20"
					>
						<path
							fill-rule="evenodd"
							d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
							clip-rule="evenodd"
						/>
					</svg>
				</div>
				<div class="ml-3">
					<p class="text-sm text-amber-700">{t('unsaved_changes')}</p>
				</div>
			</div>
		</div>
	{/if}

	<form
		id="chatbot-section-settings-form"
		bind:this={settingsForm}
		action="?/update_system_setting"
		method="POST"
		use:enhance={enhanceOptions}
		on:submit={handleSubmit}
		class="hidden"
	>
		<input id="chatbot-section-settings-input" type="hidden" name="settings" value="" />
	</form>
</div>

<!-- <div class="space-y-4 p-6 bg-white rounded-lg shadow-md mt-6"> 
    <div>
        <h2 class="text-xl font-medium text-gray-700">{t('chatbot_workflow')}</h2>
        <p class="text-sm text-gray-500">{t('chatbot_workflow_description')}</p>
    </div>

    <p class="text-sm text-gray-500">{t('underprogress')}</p>
</div> -->
