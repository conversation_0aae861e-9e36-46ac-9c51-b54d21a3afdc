<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import {
		Chart,
		registerables,
		type Chart as ChartJsInstance,
		type ChartData,
		type ChartOptions
	} from 'chart.js';

	// Import shared utilities
	import { validateNumericFields, createDataValidator } from '$lib/utils/chartValidation';
	import { createChartManager } from '$lib/utils/chartLifecycle';
	import { createLineHoverPlugin } from '$lib/utils/chartPlugins';

	// Register all Chart.js components globally.
	Chart.register(...registerables);

	// Define the interface for a generic single-line chart data item
	interface LineChartDataItem {
		label: string; // The label for the X-axis (e.g., date, month)
		value: number; // The value for the Y-axis
	}

	// Component Props
	export let data: LineChartDataItem[];
	export let chartLabel: string = 'Value'; // Label for the dataset (shown in tooltip/legend)
	export let lineColor: string = '#3B82F6'; // Color of the line
	export let showDataLabels: boolean = false; // Whether to display data labels on the chart points

	// Reference to the canvas element
	let chartEl: HTMLCanvasElement;

	// Chart.js instance (managed by chartManager, but declared here for reactivity)
	let chart: ChartJsInstance<'line'> | null = null;

	// Reactive variables for Chart.js data and options
	let chartJsData: ChartData<'line'>;
	let chartJsOptions: ChartOptions<'line'>;

	// Create custom validator for line chart data
	const validateGenericLineChartData = createDataValidator<LineChartDataItem>([
		(items) => items.every((item) => typeof item.label === 'string' && item.label.length > 0),
		(items) => items.every((item) => validateNumericFields(item, ['value']))
	]);

	// Initialize chart manager with initial state
	const chartManager = createChartManager<LineChartDataItem[], 'line'>(
		{
			chart: null,
			initialized: false,
			isLoading: true,
			error: ''
		},
		(d) => validateGenericLineChartData(d)
	);

	// Subscribe to the chartManager store for reactivity
	$: ({ chart, initialized, isLoading, error } = $chartManager);

	// Reactive block to prepare chart data and options whenever `data` prop or style props change
	$: {
		if (!Array.isArray(data)) {
			console.error('Data prop is not an array:', data);
			chartJsData = { labels: [], datasets: [] };
			chartJsOptions = {};
			chartManager.setState({ error: 'Data prop is not an array', isLoading: false });
		} else {
			const labels = data.map((item) => item.label);
			const values = data.map((item) => item.value);

			chartJsData = {
				labels: labels,
				datasets: [
					{
						label: chartLabel,
						data: values,
						borderColor: lineColor,
						backgroundColor: lineColor,
						fill: false,
						tension: 0.4, // Smoothness of the line
						pointRadius: 3,
						pointBackgroundColor: lineColor,
						pointBorderColor: '#fff',
						pointHoverRadius: 5
					}
				]
			};

			chartJsOptions = {
				responsive: true,
				maintainAspectRatio: false,
				scales: {
					x: {
						grid: {
							display: true,
							color: 'rgba(200, 200, 200, 0.4)'
						},
						ticks: {
							font: {
								size: 12
							}
						}
					},
					y: {
						beginAtZero: true,
						grid: {
							display: true,
							color: 'rgba(200, 200, 200, 0.4)'
						},
						ticks: {
							font: {
								size: 12
							}
						}
					}
				},
				plugins: {
					legend: {
						display: false // Typically no legend for single-line charts unless specified
					},
					tooltip: {
						mode: 'index',
						intersect: false
					},
					datalabels: {
						display: showDataLabels ? 'auto' : false,
						align: 'end',
						anchor: 'end',
						formatter: (value) => value // Display the raw value
					}
				}
			};
		}

		// --- Reactive Logic for Chart Initialization ---
		if (chartEl && !initialized && validateGenericLineChartData(data)) {
			console.log('--- Attempting to initialize LineChart ---');
			chartManager.initialize({
				chartEl: chartEl,
				createChart: () =>
					new Chart(chartEl, {
						type: 'line',
						data: chartJsData,
						options: chartJsOptions,
						plugins: [createLineHoverPlugin()] // Use the line hover plugin
					}),
				validateData: () => validateGenericLineChartData(data),
				onSuccess: () => console.log('LineChart initialized successfully'),
				onError: (err) => console.error('LineChart initialization failed:', err)
			});
		}

		// --- Reactive Logic for Chart Updates ---
		if (chart && initialized && chartJsData && chartJsOptions) {
			chartManager.update(chartJsData, chartJsOptions);
		}

		// --- Reactive Logic for Data Change Handling (Validation/Reset) ---
		chartManager.handleDataChange(data);
	}

	// Lifecycle Hooks
	onMount(() => {
		console.log('LineChart mounted. Initial data prop:', data);
	});

	onDestroy(() => {
		chartManager.destroy(); // Ensure chart instance is destroyed on component unmount
	});
</script>

<div class="chart-container">
	<canvas bind:this={chartEl} aria-label="Line chart"></canvas>

	{#if isLoading}
		<div class="status-overlay loading-overlay">
			<div class="spinner"></div>
			<p>Loading chart...</p>
		</div>
	{:else if error}
		<div class="status-overlay error-overlay">
			<p class="error-message">{error}</p>
			<button
				on:click={() => {
					chartManager.setState({ error: '', isLoading: true, initialized: false, chart: null });
				}}
			>
				Retry
			</button>
		</div>
	{/if}
</div>

<style>
	.chart-container {
		width: 100%;
		height: 100%;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.status-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: rgba(255, 255, 255, 0.9);
		z-index: 10;
		gap: 1rem;
	}

	.loading-overlay {
		color: #6b7280;
	}

	.spinner {
		width: 40px;
		height: 40px;
		border: 4px solid #e5e7eb;
		border-top: 4px solid #3b82f6;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	.error-overlay {
		color: #dc2626;
		text-align: center;
		padding: 2rem;
		background-color: #fef2f2;
		border: 1px solid #fecaca;
		border-radius: 8px;
	}

	.error-message {
		color: #dc2626;
		font-weight: 500;
		margin: 0;
	}

	button {
		padding: 0.5rem 1rem;
		background-color: #3b82f6;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-size: 0.875rem;
		transition: background-color 0.2s;
	}

	button:hover {
		background-color: #2563eb;
	}

	canvas {
		max-width: 100%;
		max-height: 100%;
	}
</style>