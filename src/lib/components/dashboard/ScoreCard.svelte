<script lang="ts">
    import { COLORS } from '$lib/components/dashboard/colors'; // Ensure this path is correct

    export let title: string;
    export let value: number | string;
    export let unit: string = ''; // Optional unit (e.g., %, min)
    export let description: string = '';
    export let valueColor: string = 'text-gray-900'; // Tailwind class for value color
    export let trendValue: number | undefined = undefined; // Optional trend value for arrows
    export let trendUnit: string = ''; // Unit for trend value
</script>

<div class="flex flex-col h-full">
    <div class="flex items-center justify-between mb-2">
        <h2 class="text-xl font-semibold text-gray-700">{title}</h2>
    </div>
    <div class="flex items-baseline mb-1">
        <p class="text-4xl font-bold {valueColor} leading-none">{value}</p>
        {#if unit}
            <span class="ml-2 text-xl font-semibold text-gray-500">{unit}</span>
        {/if}
    </div>
    <p class="text-sm text-gray-500 mb-4 flex-grow">{description}</p>

    {#if trendValue !== undefined}
        <div class="flex items-center text-sm font-medium">
            {#if trendValue > 0}
                <span class="text-green-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path></svg>
                    +{trendValue}{trendUnit}
                </span>
            {:else if trendValue < 0}
                <span class="text-red-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path></svg>
                    {trendValue}{trendUnit}
                </span>
            {:else}
                <span class="text-gray-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h8m-4 4v-8"></path></svg>
                    {trendValue}{trendUnit}
                </span>
            {/if}
            <span class="ml-1 text-gray-500">เทียบกับช่วงก่อนหน้า</span>
        </div>
    {/if}
</div>
