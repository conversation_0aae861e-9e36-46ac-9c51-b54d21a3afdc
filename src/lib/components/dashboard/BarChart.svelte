<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import {
        Chart,
        registerables,
        type Chart as ChartJsInstance,
        type ChartData,
        type ChartOptions,
        type ScriptableContext
    } from 'chart.js';
    import ChartDataLabels from 'chartjs-plugin-datalabels';

    import { validateArrayStructure, validateNumericFields, createDataValidator } from '$lib/utils/chartValidation';
    import { createChartManager } from '$lib/utils/chartLifecycle';
    import { createHoverShadowPlugin } from '$lib/utils/chartPlugins';

    import { COLORS } from '$lib/components/dashboard/colors';

    Chart.register(...registerables, ChartDataLabels);

    // REVISED: Make 'label' and 'value' optional to accommodate flexible data structures
    interface BarChartDataItem {
        label?: string; // Made optional
        value?: number; // Made optional
        [key: string]: any; // Allows for additional properties like 'agentName', 'responseTime', etc.
    }

    interface GroupedKey {
        key: string;
        label: string;
        color: string;
    }

    export let data: BarChartDataItem[];
    export let label: string = 'Value';
    export let barColor: string | string[] | ((context: ScriptableContext<'bar'>) => string) = COLORS.blue;
    export let borderColor: string | string[] | ((context: ScriptableContext<'bar'>) => string) | undefined = undefined;
    export let borderWidth: number = 0;
    export let chartType: 'verticalBar' | 'horizontalBar' | 'stackedVerticalBar' | 'stackedHorizontalBar' | 'groupedBar' = 'verticalBar';
    export let labelKey: string = 'label';
    export let valueKey: string = 'value';

    export let stackedKeys: string[] = [];
    export let stackedColors: { [key: string]: string } = {};

    export let groupedKeys: GroupedKey[] = [];

    export let showValueLabels: boolean = false;

    let chartEl: HTMLCanvasElement;
    let chart: ChartJsInstance<'bar'> | null = null;
    let chartJsData: ChartData<'bar'>;
    let chartJsOptions: ChartOptions<'bar'>;

    const validateBarChartData = createDataValidator<BarChartDataItem>([
        (items) => validateArrayStructure(items),
        // Adjusted validation logic to be more flexible based on chartType
        (items) => {
            if (items.length === 0) return true; // Empty array is valid for no data state

            // Ensure labelKey exists and is a string for all chart types that use it
            if (!items.every(item => typeof item[labelKey] === 'string' && item[labelKey].length > 0)) {
                console.warn(`Validation failed: Missing or invalid 'labelKey' (${labelKey}) in some data items.`);
                return false;
            }

            if (chartType === 'groupedBar') {
                if (groupedKeys.length === 0) {
                    console.warn(`Validation failed for groupedBar: 'groupedKeys' prop is empty.`);
                    return false;
                }
                return items.every(item => groupedKeys.every(gk => {
                    const isValid = typeof item[gk.key] === 'number' && !isNaN(item[gk.key]);
                    if (!isValid) {
                        console.warn(`Validation failed for groupedBar: Non-numeric or missing key '${gk.key}' in data item:`, item);
                    }
                    return isValid;
                }));
            } else if (stackedKeys && stackedKeys.length > 0) {
                if (!items.every(item => stackedKeys.every(sk => typeof item[sk] === 'number' && !isNaN(item[sk])))) {
                    console.warn(`Validation failed for stacked chart: Missing or non-numeric stacked key in some data items.`);
                    return false;
                }
            } else { // verticalBar, horizontalBar (simple bar charts)
                if (!items.every(item => typeof item[valueKey] === 'number' && !isNaN(item[valueKey]))) {
                    console.warn(`Validation failed for simple bar chart: Missing or non-numeric 'valueKey' (${valueKey}) in some data items.`);
                    return false;
                }
            }
            return true;
        }
    ]);

    const chartManager = createChartManager<BarChartDataItem[], 'bar'>(
        {
            chart: null,
            initialized: false,
            isLoading: true,
            error: ''
        },
        (d) => validateBarChartData(d)
    );

    $: ({ chart, initialized, isLoading, error } = $chartManager);

    $: {
        if (!Array.isArray(data)) {
            console.error('BarChart: Data prop is not an array:', data);
            chartJsData = { labels: [], datasets: [] };
            chartJsOptions = {};
            chartManager.setState({ error: 'Data prop is not an array', isLoading: false });
        } else if (data.length === 0) { // Removed redundant check as validateBarChartData handles empty
             console.warn('BarChart: Data array is empty. Chart will show no data.');
             chartJsData = { labels: [], datasets: [] };
             chartJsOptions = {};
             chartManager.setState({ error: 'No data to display.', isLoading: false });
        }
         else {
            const labels = data.map(item => item[labelKey]);
            let datasets: any[] = [];
            let isStacked = false;
            let showLegend = false;
            let indexAxis: 'x' | 'y' = 'x';

            if (chartType === 'groupedBar') {
                if (groupedKeys.length === 0) {
                    console.warn("BarChart: chartType is 'groupedBar' but 'groupedKeys' prop is empty. Chart will be empty.");
                    datasets = [];
                    chartManager.setState({ error: "Configuration error: 'groupedKeys' is empty for groupedBar chart.", isLoading: false });
                } else {
                    datasets = groupedKeys.map(group => ({
                        label: group.label,
                        data: data.map(item => item[group.key]),
                        backgroundColor: group.color,
                        borderColor: group.color,
                        borderWidth: borderWidth,
                    }));
                    showLegend = true;
                    indexAxis = 'x';
                }
            } else if (stackedKeys && stackedKeys.length > 0) {
                datasets = stackedKeys.map(key => ({
                    label: key.charAt(0).toUpperCase() + key.slice(1),
                    data: data.map(item => item[key]),
                    backgroundColor: stackedColors[key] || COLORS.gray,
                    borderColor: stackedColors[key] || COLORS.gray,
                    borderWidth: borderWidth
                }));
                isStacked = true;
                showLegend = true;
                indexAxis = (chartType === 'stackedHorizontalBar') ? 'y' : 'x';
            } else {
                datasets = [
                    {
                        label: label,
                        data: data.map(item => item[valueKey]),
                        backgroundColor: barColor,
                        borderColor: borderColor || barColor,
                        borderWidth: borderWidth,
                        hoverBackgroundColor: typeof barColor === 'function'
                            ? (ctx: ScriptableContext<'bar'>) => {
                                const originalColor = barColor(ctx);
                                if (typeof originalColor === 'string') {
                                    return originalColor + 'CC';
                                }
                                return originalColor;
                            }
                            : undefined,
                    }
                ];
                indexAxis = (chartType === 'horizontalBar') ? 'y' : 'x';
            }

            chartJsData = {
                labels: labels,
                datasets: datasets
            };

            chartJsOptions = {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: indexAxis,
                scales: {
                    x: {
                        stacked: isStacked,
                        title: {
                            display: indexAxis === 'y' && !!label,
                            text: indexAxis === 'y' ? label : undefined,
                        },
                        grid: {
                            display: true,
                            color: 'rgba(200, 200, 200, 0.4)'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        stacked: isStacked,
                        title: {
                            display: indexAxis === 'x' && !!label,
                            text: indexAxis === 'x' ? label : undefined,
                        },
                        grid: {
                            display: true,
                            color: 'rgba(200, 200, 200, 0.4)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: showLegend,
                        position: 'top',
                        labels: {
                            font: {
                                size: 14
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let tooltipLabel = context.dataset.label || '';
                                if (tooltipLabel) {
                                    tooltipLabel += ': ';
                                }

                                let value: number;
                                // Determine the raw value based on chart axis
                                if (context.chart.options.indexAxis === 'y') {
                                    value = context.parsed.x;
                                } else {
                                    value = context.parsed.y;
                                }

                                if (value !== null) {
                                    // ✨ ADDED LOGIC FOR STACKED BAR PERCENTAGE IN TOOLTIP
                                    if (stackedKeys.length > 0) {
                                        let total = 0;
                                        // Calculate the total for the current stack at this dataIndex
                                        const dataIndex = context.dataIndex;
                                        context.chart.data.datasets.forEach((dataset: any) => {
                                            if (dataset.data[dataIndex] !== undefined) {
                                                total += dataset.data[dataIndex];
                                            }
                                        });

                                        if (total === 0) {
                                            tooltipLabel += '0%';
                                        } else {
                                            const percentage = ((value / total) * 100).toFixed(0); // Format as whole number percentage
                                            tooltipLabel += percentage + '%';
                                        }
                                    } else {
                                        // Existing logic for non-stacked bars
                                        tooltipLabel += value;
                                    }
                                }

                                // Add units based on the chart label/context (keep this existing logic)
                                if (context.dataset.label === 'Average CSAT') {
                                    tooltipLabel += ' out of 5';
                                } else if (context.dataset.label === 'Avg. First Response Time') {
                                    tooltipLabel += ' sec';
                                } else if (context.dataset.label === 'FCR %' || context.dataset.label === 'Escalation %') {
                                     tooltipLabel += '%';
                                } else if (context.dataset.label === 'Time (min)') {
                                     tooltipLabel += ' min';
                                }
                                return tooltipLabel;
                            }
                        }
                    },
                    // --- DATALABELS PLUGIN CONFIGURATION ---
                    datalabels: {
                        display: showValueLabels,
                        color: '#FFFFFF',
                        anchor: 'end',
                        align: (context) => {
                            if (indexAxis === 'y') { // Horizontal bar
                                return 'start';
                            }
                            return 'start';
                        },
                        offset: -3,
                        font: {
                            weight: 'normal',
                            size: 12,
                        },
                        formatter: function(value: any, context: any) {
                            if (chartType === 'groupedBar') {
                                return value.toString();
                            } else if (stackedKeys.length > 0) {
                                // Get the current data point's index and dataset index
                                const dataIndex = context.dataIndex;

                                // Calculate the total for the current stack at this dataIndex
                                let total = 0;
                                context.chart.data.datasets.forEach((dataset: any) => {
                                    // Ensure we're summing values from the same stack
                                    // and that the dataset has data for this index
                                    if (dataset.data[dataIndex] !== undefined) {
                                        total += dataset.data[dataIndex];
                                    }
                                });

                                if (total === 0) {
                                    return '0%'; // Avoid division by zero
                                }

                                const percentage = ((value / total) * 100).toFixed(0);
                                return percentage + '%';
                            } else {
                                // For simple bar charts, show the main value as a string
                                return value.toString();
                            }
                        }
                    }
                    // --- END DATALABELS PLUGIN CONFIGURATION ---
                }
            };
        }

        // IMPORTANT: The validation check in the reactivity block was sometimes causing issues
        // with initial chart creation if data wasn't immediately perfectly valid or if empty.
        // Let's refine the logic for initialization and updates.
        if (chartEl) {
            const isValidData = validateBarChartData(data);

            if (!initialized && isValidData) {
                console.log('BarChart: Attempting to initialize chart...');
                chartManager.initialize({
                    chartEl: chartEl,
                    createChart: () => {
                        console.log('BarChart: Creating new Chart.js instance with data:', chartJsData);
                        return new Chart(chartEl, {
                            type: 'bar',
                            data: chartJsData,
                            options: chartJsOptions,
                            plugins: [ChartDataLabels, createHoverShadowPlugin()] // Ensure datalabels is in plugins array
                        });
                    },
                    validateData: () => validateBarChartData(data),
                    onSuccess: () => console.log('BarChart initialized successfully.'),
                    onError: (err) => console.error('BarChart initialization failed:', err)
                });
            } else if (chart && initialized) {
                // Only update if data is valid and there's a chart to update
                if (isValidData) {
                    console.log('BarChart: Updating existing chart with new data.');
                    chartManager.update(chartJsData, chartJsOptions);
                } else {
                    // If data becomes invalid after initialization, destroy the chart and show error
                    chartManager.destroy();
                    console.log('BarChart: Destroyed chart due to invalid data during update.');
                    if (!error) { // Only set error if not already set by data check at top of block
                       chartManager.setState({ error: 'No valid data to display.', isLoading: false });
                    }
                }
            } else if (!isValidData && !error) {
                // If data is invalid on first render and no error is set yet
                chartManager.setState({ error: 'No valid data to display.', isLoading: false });
            }
        }
    }

    onMount(() => {
        console.log('BarChart mounted. Initial data prop:', data);
        // Initial chart creation logic is primarily handled by the reactive block now.
        // This ensures it runs after DOM is ready and data is available.
    });

    onDestroy(() => {
        console.log('BarChart destroyed.');
        chartManager.destroy();
    });
</script>

<div class="chart-container">
    <canvas bind:this={chartEl} aria-label="Bar chart"></canvas>

    {#if isLoading}
        <div class="status-overlay loading-overlay">
            <div class="spinner"></div>
            <p>Loading chart...</p>
        </div>
    {:else if error}
        <div class="status-overlay error-overlay">
            <p class="error-message">{error}</p>
            <button
                on:click={() => {
                    chartManager.setState({ error: '', isLoading: true, initialized: false, chart: null });
                }}
            >
                Retry
            </button>
        </div>
    {/if}
</div>

<style>
    .chart-container {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .status-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }

    .loading-overlay p, .error-overlay p {
        margin-top: 10px;
        color: #555;
    }

    .error-message {
        color: #d32f2f; /* Red color for error messages */
        font-weight: bold;
    }

    .spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>