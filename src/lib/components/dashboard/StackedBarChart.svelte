<script lang="ts">
	// Import Svelte lifecycle functions
	import { onMount, onDestroy, tick } from 'svelte'; // Import 'tick' for better timing control

	// Import Chart.js and its registerables
	import {
		Chart,
		registerables,
		type Chart as ChartJsInstance,
		type ChartData,
		type ChartOptions
	} from 'chart.js';
	// Import the datalabels plugin
	import ChartDataLabels from 'chartjs-plugin-datalabels';

	// Register all Chart.js components and the datalabels plugin globally.
	Chart.register(...registerables, ChartDataLabels);

	/**
	 * @type {Array<{ month: string; pass: number; notPass: number; total: number; passRate: number; }>} data - Monthly claims data.
	 */
	export let data: Array<{
		month: string;
		pass: number;
		notPass: number;
		total: number;
		passRate: number;
	}>;

	// Extract colors to constants for better maintainability
	const COLORS = {
		PASS: '#22C55E',
		NOT_PASS: '#EF4444',
		GRID: '#e0e7ff',
		TEXT: '#3b82f6',
		SHADOW: 'rgba(0, 0, 0, 0.4)'
	} as const;

	// Add validation function
	function validateData(data: typeof data): boolean {
		if (!Array.isArray(data) || data.length === 0) return false;

		return data.every(
			(item) =>
				typeof item.month === 'string' &&
				typeof item.pass === 'number' &&
				item.pass >= 0 &&
				typeof item.notPass === 'number' &&
				item.notPass >= 0 &&
				typeof item.total === 'number' &&
				item.total >= 0 &&
				typeof item.passRate === 'number' &&
				item.passRate >= 0 &&
				item.passRate <= 100 &&
				item.pass + item.notPass === item.total // Data consistency check
		);
	}

	/**
	 * @type {'counts' | 'percentages'} type - Determines if the chart shows counts (stacked) or percentages.
	 * 'counts': Shows stacked 'pass' and 'notPass' values.
	 * 'percentages': Shows a single bar for 'passRate'.
	 */
	export let type: 'counts' | 'percentages' = 'counts'; // Default to 'counts'

	/* ────── chart setup ────── */
	let chartEl: HTMLCanvasElement; // The canvas element will be bound here
	let chart: ChartJsInstance<'bar'> | null = null; // Initialize as null
	let initialized = false; // Track if the chart has been initialized

	// Declare chartData and chartOptions with their types
	let chartData: ChartData<'bar'>;
	let chartOptions: ChartOptions<'bar'>;

	// Reactive assignment for chartData based on 'type' prop
	$: chartData = {
		labels: data.map((item) => item.month),
		datasets:
			type === 'counts'
				? [
						{
							type: 'bar',
							label: 'Pass',
							data: data.map((item) => item.pass),
							backgroundColor: COLORS.PASS, // Green for pass
							stack: 'Stack 1',
							datalabels: {
								color: 'white',
								formatter: (value: number) => value.toString(),
								font: {
									size: 12,
									weight: 'bold' as const // Type assertion for string literal
								},
								align: 'center' as const, // Type assertion
								anchor: 'center' as const // Type assertion
							}
						},
						{
							type: 'bar',
							label: 'Not Pass',
							data: data.map((item) => item.notPass),
							backgroundColor: COLORS.NOT_PASS, // Red for not pass
							stack: 'Stack 1',
							datalabels: {
								color: 'white',
								formatter: (value: number) => (value > 0 ? value.toString() : ''),
								font: {
									size: 12,
									weight: 'bold' as const
								},
								align: 'center' as const,
								anchor: 'center' as const
							}
						}
					]
				: [
						{
							type: 'bar',
							label: 'Pass Rate %',
							data: data.map((item) => item.passRate),
							backgroundColor: COLORS.PASS, // Green for pass rate
							datalabels: {
								color: COLORS.PASS, // Color of the data label text
								formatter: (value: number) => value.toFixed(0) + '%', // Display value as percentage
								font: {
									size: 12,
									weight: 'bold' as const
								},
								align: 'end' as const, // Align label at the end (top) of the bar
								anchor: 'end' as const // Anchor point for the label
							}
						}
					]
	};

	/* ---- Custom Plugin for Hover Shadow ---- */
	const barHoverShadowPlugin = {
		id: 'barHoverShadow',
		beforeDatasetsDraw(chartInstance: ChartJsInstance<'bar'>) {
			try {
				const { ctx, chartArea } = chartInstance;
				const activeElements = chartInstance.getActiveElements();

				if (!activeElements || activeElements.length === 0 || !ctx || !chartArea) {
					return;
				}

				ctx.save();
				ctx.shadowColor = COLORS.SHADOW;
				ctx.shadowBlur = 15;
				ctx.shadowOffsetX = 0;
				ctx.shadowOffsetY = 8;

				activeElements.forEach((activeElement) => {
					const bar = activeElement.element as any; // Cast to 'any' for direct property access if needed

					if (!bar || typeof bar.x !== 'number' || typeof bar.width !== 'number') {
						return;
					}

					const shadowX = bar.x - bar.width / 2;
					const shadowWidth = bar.width;
					const padding = 2;

					ctx.fillStyle = 'rgba(0, 0, 0, 0.2)'; // Shadow fill color
					ctx.fillRect(
						shadowX - padding,
						chartArea.top - padding, // Shadow starts at top of chart area
						shadowWidth + padding * 2,
						chartArea.height + padding * 2 // Shadow covers full chart height
					);
				});

				ctx.restore();
			} catch (error) {
				console.error('Error in barHoverShadowPlugin:', error);
			}
		}
	};

	// Reactive assignment for chartOptions based on 'type' prop
	$: chartOptions = {
		responsive: true,
		maintainAspectRatio: false,
		scales: {
			x: {
				stacked: type === 'counts', // Stacked only for 'counts' type
				grid: {
					display: false
				},
				ticks: {
					color: COLORS.TEXT,
					font: {
						size: 14,
						weight: 'bold' as const
					}
				}
			},
			y: {
				stacked: type === 'counts', // Stacked only for 'counts' type
				beginAtZero: true,
				// Calculate max based on total (for counts) or 100 (for percentages)
				max: 100,
				grid: {
					color: COLORS.GRID,
					borderDash: [3, 3]
				},
				ticks: {
					color: COLORS.TEXT,
					// Dynamic step size for counts, fixed for percentages
					stepSize: 25,
					font: {
						size: 12
					},
					// Format Y-axis ticks as percentages only for 'percentages' type
					callback:
						type === 'percentages'
							? function (value: number | string) {
									return value + '%';
								}
							: undefined // No custom callback for 'counts'
				}
			}
		},
		plugins: {
			legend: {
				display: false, // Hide the default legend
				position: 'bottom',
				labels: {
					boxWidth: 20,
					padding: 20,
					font: {
						size: 14
					}
				}
			},
			tooltip: {
				enabled: true,
				mode: 'index' as const,
				intersect: false,
				callbacks: {
					title: function (context: any) {
						return context[0].label; // Month
					},
					label: function (context: any) {
						const datasetLabel = context.dataset.label || '';
						const value = context.parsed.y;
						// Customize label based on chart type
						if (type === 'percentages') {
							return `${datasetLabel} : ${value.toFixed(2)}%`;
						} else {
							return `${datasetLabel} : ${value}`;
						}
					},
					// Show footer (total) only for 'counts' type
					footer:
						type === 'counts'
							? function (context: any) {
									const index = context[0].dataIndex;
									// Safely access data[index].total
									const totalForMonth = data[index] ? data[index].total : 0;
									return `Total : ${totalForMonth}`;
								}
							: undefined // No footer for 'percentages'
				}
			},
			datalabels: {
				// Global datalabels settings (can be overridden per dataset)
			}
		}
	} as ChartOptions<'bar'>; // Type assertion for chartOptions

	// Component state for UI feedback
	let isLoading = true;
	let error = '';

	// Function to initialize the chart
	async function initializeChart() {
		try {
			// Prevent re-initialization if already initialized or if critical elements are missing/invalid
			if (initialized || !chartEl || !validateData(data)) {
				return;
			}

			// Ensure DOM is fully ready after initial render cycle
			await tick();

			// Double-check chartEl after tick, especially important for conditional rendering
			if (!chartEl) {
				console.warn('Chart element not found after tick, deferring initialization.');
				return; // Exit if still not available
			}

			// Destroy existing chart instance if it somehow persists
			if (chart) {
				chart.destroy();
				chart = null;
			}

			// Create new Chart.js instance
			chart = new Chart(chartEl, {
				type: 'bar',
				data: chartData,
				options: chartOptions,
				plugins: [barHoverShadowPlugin]
			});

			initialized = true; // Mark as initialized
			isLoading = false; // Hide loading state
			error = ''; // Clear any previous errors
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to initialize chart';
			isLoading = false;
			initialized = false; // Reset initialized flag on error
			console.error(`Chart initialization error:`, err);
		}
	}

	// Reactive statement to trigger chart initialization
	// This runs whenever `chartEl` becomes truthy (i.e., the canvas is mounted),
	// `initialized` is false, and `data` is valid.
	$: if (chartEl && !initialized && validateData(data)) {
		initializeChart();
	}

	// Reactivity: Update chart data and options if props change
	// This handles subsequent updates after initial creation
	let updateTimeout: number;
	$: if (chart && initialized && (data || type)) {
		// Ensure chart is initialized before updating
		clearTimeout(updateTimeout);
		updateTimeout = setTimeout(() => {
			try {
				// Safely update chart data and options
				if (chart && chartData && chartOptions) {
					chart.data = chartData;
					chart.options = chartOptions;
					chart.update('none'); // 'none' for instant update without animation
				}
			} catch (err) {
				console.error('Error updating chart:', err);
			}
		}, 50); // Debounce updates to avoid flickering
	}

	// Reactive block to handle data becoming invalid or other state changes that require re-initialization
	$: {
		if (!validateData(data) && initialized) {
			// If data becomes invalid after chart was initialized, destroy it
			if (chart) {
				try {
					chart.destroy();
				} catch (err) {
					console.error('Error destroying chart due to invalid data:', err);
				}
				chart = null;
			}
			initialized = false;
			isLoading = false; // Stop loading spinner if it was active
			error = 'Invalid data provided to chart.';
		} else if (validateData(data) && !initialized && chartEl) {
			// If data becomes valid and chart is not initialized, try initializing
			initializeChart();
		}
	}

	// onMount lifecycle hook: Primarily for setting up things that need to happen once on mount,
	// though chart initialization is now handled reactively.
	onMount(async () => {
		try {
			// Wait for next tick to ensure DOM is fully rendered
			await tick();

			if (!chartEl) {
				throw new Error('Canvas element not found');
			}

			if (!validateData(data)) {
				throw new Error('Invalid data');
			}

			chart = new Chart(chartEl, {
				type: 'bar',
				data: chartData,
				options: chartOptions,
				plugins: [barHoverShadowPlugin]
			});

			isLoading = false;
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to initialize chart';
			isLoading = false;
		}
	});

	// onDestroy lifecycle hook: Cleans up the chart instance when the component is removed
	onDestroy(() => {
		if (updateTimeout) {
			clearTimeout(updateTimeout);
		}
		if (chart) {
			try {
				chart.destroy(); // Safely destroy the chart instance
			} catch (err) {
				console.error('Error destroying chart on component unmount:', err);
			}
			chart = null; // Clear the reference
			initialized = false; // Reset initialized flag
		}
	});
</script>

<div
	class="chart-container"
	style="width: 100%; height: 100%; min-height: 300px;"
	role="img"
	aria-label="Bar chart showing {type === 'counts'
		? 'pass and not pass counts'
		: 'pass rates'} by month"
>
	<canvas bind:this={chartEl} aria-label="Interactive bar chart with {data.length} data points"
	></canvas>

	{#if isLoading}
		<div class="status-overlay loading-overlay">
			<div class="spinner"></div>
			<span>Loading chart...</span>
		</div>
	{:else if error}
		<div class="status-overlay error-overlay">
			<span>❌ Error: {error}</span>
			<button
				on:click={() => {
					error = '';
					isLoading = true;
					initialized = false;
					if (chart) {
						chart.destroy();
						chart = null;
					}
					// The reactive initialization block will pick up from here
				}}
			>
				Retry
			</button>
		</div>
	{/if}
</div>

<style>
	.chart-container {
		display: flex; /* Still useful for initial centering of canvas if needed */
		justify-content: center;
		align-items: center;
		position: relative; /* Crucial for absolute positioning of overlays */
		width: 100%;
		height: 100%;
		min-height: 300px;
	}

	/* Ensure canvas takes full space within its container */
	.chart-container canvas {
		width: 100% !important; /* Override Chart.js default inline styles */
		height: 100% !important; /* Override Chart.js default inline styles */
		display: block; /* Remove any extra space below canvas */
	}

	.status-overlay {
		position: absolute; /* Position over the canvas */
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		gap: 12px;
		font-size: 16px;
		background-color: rgba(255, 255, 255, 0.9); /* Semi-transparent background */
		z-index: 10; /* Ensure it's above the canvas */
	}

	.loading-overlay {
		color: #6b7280;
	}

	.error-overlay {
		color: #ef4444;
		text-align: center;
	}

	.error-overlay button {
		padding: 8px 16px;
		background: #ef4444;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-size: 14px;
	}

	.error-overlay button:hover {
		background: #dc2626;
	}

	.spinner {
		width: 24px;
		height: 24px;
		border: 3px solid #f3f4f6;
		border-top: 3px solid #3b82f6;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
</style>
