<script lang="ts">
	import type { Customer } from '$lib/types/customer';
	import { services } from "$src/lib/api/features";
	import { onMount } from 'svelte';
	import { Timeline, TimelineItem } from "flowbite-svelte";
	import { t, language } from '$lib/stores/i18n';
	import { get } from 'svelte/store';

	import { getColorClass, formatTimestamp, getSentimentIcon } from '$lib/utils';


	export let customer: Customer;
	export let platformId: number;
	export let access_token: string;

	interface TicketAnalysis {
		id: number;
		ticket: number;
		sentiment: string;
		summary: string;
		total_cost: number;
		total_tokens: number;
		is_faq: boolean;
		is_recommendation: boolean;
		is_renewal: boolean;
		is_claim: boolean;
		is_complain: boolean;
		is_insurance_policy: boolean;
		// case_types: Record<string, number>;
		// case_topics: Record<string, number>;
		highlights: Array<{
			id: number;
			sentence: string;
			order: number;
		}>;
		created_on: string;
	}

	interface CustomerSummaryData {
		customer_id: number;
		customer_name: string;
		total_count: number;
		limit: number;
		offset: number;
		has_more: boolean;
		sentiment_statistics: {
			Positive: number;
			Neutral: number;
			Negative: number;
		};
		average_tokens: number;
		average_cost: number;
		analyses: TicketAnalysis[];
	}

	let summaryData: CustomerSummaryData | null = null;
	let loading = true;
	let error = '';

	const getSentimentColor = (sentiment: string) => {
		switch (sentiment) {
			case 'Positive':
				return 'text-green-600 bg-green-100';
			case 'Negative':
				return 'text-red-600 bg-red-100';
			case 'Neutral':
				return 'text-gray-600 bg-gray-100';
			default:
				return 'text-gray-600 bg-gray-100';
		}
	};

	const getAnalysisCategories = (analysis: TicketAnalysis) => {
		const categories = [];
		if (analysis.is_faq) categories.push('FAQ');
		if (analysis.is_recommendation) categories.push('Recommendation');
		if (analysis.is_renewal) categories.push('Renewal');
		if (analysis.is_claim) categories.push('Claim');
		if (analysis.is_complain) categories.push('Complaint');
		if (analysis.is_insurance_policy) categories.push('Insurance Policy');
		return categories;
	};

	const getTicketTitle = (analysis: TicketAnalysis) => {
		if (analysis.is_complain) return 'Ticket Complaint';
		if (analysis.is_claim) return 'Ticket Claim';
		if (analysis.is_renewal) return 'Ticket Renewal';
		if (analysis.is_faq) return 'Ticket FAQ';
		if (analysis.is_recommendation) return 'Ticket Recommendation';
		if (analysis.is_insurance_policy) return 'Ticket Policy';
		return 'Ticket Transferred'; // default case
	};

	async function loadCustomerSummary() {
		try {
			loading = true;
			error = '';
			
			if (!access_token) {
				throw new Error('No access token provided');
			}

			const result = await services.customers.getCustomerTicketAnalyses(customer.customer_id, access_token);
			
			if (result.res_status === 200) {
				summaryData = result.ticket_analyses;
			} else {
				throw new Error(result.error_msg || 'Failed to load customer summary');
			}

			console.log(summaryData)

		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to load customer summary';
		} finally {
			loading = false;
		}
	}

	onMount(() => {
		loadCustomerSummary();
	});

	const lang = get(language); // ดึงค่าภาษา ('en' หรือ 'th')
	$: currentLangName = 
		$language === 'en' ? t('language_name_en') :
		$language === 'th' ? t('language_name_th') :
		$language;

	// Helper function to format dictionary entries
	const formatDictionary = (dict: Record<string, number> | undefined) => {
		if (!dict || Object.keys(dict).length === 0) return [];
		return Object.entries(dict).map(([key, value]) => ({ key, value }));
	};
</script>

<div class="p-6">
	{#if loading}
		<div class="text-center py-8">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
			<p class="mt-2 text-sm text-gray-500">{t('loading_customer_summary')}</p>
		</div>
	{:else if error}
		<div class="text-center py-8">
			<svg class="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
			</svg>
			<h3 class="mt-2 text-sm font-medium text-gray-900">{t('error_loading_summary')}</h3>
			<p class="mt-1 text-sm text-gray-500">{error}</p>
			<button 
				on:click={loadCustomerSummary}
				class="mt-4 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200"
			>
				{t('try_again')}
			</button>
		</div>
	{:else if !summaryData || summaryData.total_count === 0}
		<div class="text-center text-gray-500 mt-8">
			<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
			</svg>
			<h3 class="mt-2 text-sm font-medium text-gray-900">{t('no_analysis_available')}</h3>
			<p class="mt-1 text-sm text-gray-500">{t('ticket_analysis_placeholder')}</p>
		</div>
	{:else}
		<!-- Header with Filter Tabs -->
		<div class="flex items-center justify-between mb-6">
			<h2 class="text-2xl font-bold text-gray-900">{t('ticket_summary')}</h2>
			
			<!-- Filter Tabs -->
			<!-- <div class="border-b border-gray-200">
				<nav class="-mb-px flex space-x-8">
					<button class="border-b-2 border-gray-900 py-2 px-1 text-sm font-medium text-gray-900">
						{t('all_events')}
					</button>
					<button class="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700">
						{t('transfers')}
					</button>
					<button class="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700">
						{t('closures')}
					</button>
				</nav>
			</div> -->
		</div>


		<!-- Timeline using Flowbite components -->
		<Timeline>
			{#each summaryData.analyses as analysis}
				<TimelineItem >
					<svelte:fragment slot="icon">
						<div class={`w-3 h-3 rounded-full ${analysis.sentiment === 'Positive' ? 'bg-green-500' : analysis.sentiment === 'Negative' ? 'bg-red-500' : 'bg-gray-400'}`}></div>
					</svelte:fragment>

					<!-- Card Content -->
					<div class="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
						<div class="flex items-center justify-between mb-2">
							<div class="flex items-center gap-2">
							  <span class="text-md font-semibold">{t(analysis.action)}</span>
							</div>
							<div class="text-sm text-gray-500">{formatTimestamp(analysis.created_on)}</div>
						</div>

						<div class="flex items-center justify-between mb-2">
							<div class="flex items-center space-x-2">
								<span class="rounded-full border px-3 py-1 text-sm font-bold text-black bg-gray-100">{t('ticket')}-{analysis.ticket}</span>
								
								<!-- Header with sentiment and categories -->
								<!-- <div class="flex items-start justify-between mb-2"> -->
								<!-- <div class="flex items-center space-x-2"> -->
								<div class={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-semibold border transition-all duration-200 hover:shadow-md ${getSentimentColor(analysis.sentiment)}`}>
									<img
										src={getSentimentIcon(analysis.sentiment)}
										alt={analysis.sentiment}
										class="w-5 h-5 mr-2"
									/>
									{analysis.sentiment}
								</div>
								<!-- {#each getAnalysisCategories(analysis) as category}
									<span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-50 text-gray-700 border border-gray-200 hover:border-gray-300 hover:bg-gray-100 transition-all duration-200">
										{category}
									</span>
								{/each} -->
								<!-- </div> -->
								<!-- </div> -->
							</div>
						</div>

						<!-- Summary -->
						<div class="mb-4">
							{#if lang === 'en'}
								<p class="text-sm text-gray-700 leading-relaxed">{analysis.summary.english}</p>
							{:else if lang === 'th'}
								<p class="text-sm text-gray-700 leading-relaxed">{analysis.summary.thai}</p>
							{:else}
								<!-- fallback สำหรับภาษาที่ไม่รู้จัก -->
								<p class="text-sm text-gray-700 leading-relaxed">{analysis.summary.english}</p>
							{/if}
							<p class="text-sm text-gray-700 leading-relaxed">{t('translation')}: {currentLangName}</p>
						</div>

						<!-- Ticket Topics - Only show when action is "close-ticket" -->
						{#if analysis.action === 'close-ticket'}
							<div class="flex items-center justify-between text-xs text-gray-500 pt-3 border-t border-gray-100">
								<!-- Ticket Topics -->
								{#if analysis.ticket_topics && analysis.ticket_topics.length > 0}
									<div class="space-y-2">
										{#each [...new Set(analysis.ticket_topics.map(t => t.case_type))] as caseType}
											<div>	
												<span class="text-sm font-semibold text-gray-700 mt-2">{t('case_type')}</span>
												<div class="flex flex-wrap gap-2 mt-1">
													<span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-600 border border-gray-300">
														<!-- {caseType} -->
														 {#if lang === 'th'}
															{t(caseType.toLowerCase().replace(/[\s\/-]+/g, '_'))} 
															({caseType})
														{:else}
															{caseType}
														{/if}
													</span>
												</div>
											</div>
											<div>	
												<span class="text-sm font-semibold text-gray-700 mt-2">{t('case_topic')}</span>
												<div class="flex flex-wrap gap-2 mt-1">
													{#each analysis.ticket_topics.filter(t => t.case_type === caseType) as topic}
														<span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-600 border border-gray-300">
															<!-- {topic.case_topic} -->
															{#if lang === 'th'}
																{t(topic.case_topic.toLowerCase().replace(/[\s\/-]+/g, '_'))} 
																({topic.case_topic})
															{:else}
																{topic.case_topic}
															{/if}
														</span>
													{/each}
												</div>
											</div>
										{/each}
									</div>
								{/if}
							</div>
						{/if}
						<!-- Highlights -->
						<!-- {#if analysis.highlights && analysis.highlights.length > 0}
							<div class="mb-4">
								<h5 class="text-sm font-medium text-gray-900 mb-2">Key Highlights:</h5>
								<div class="space-y-2">
									{#each analysis.highlights.sort((a, b) => a.order - b.order).slice(0, 3) as highlight}
										<div class="bg-yellow-50 border-l-4 border-yellow-400 p-2">
											<p class="text-sm text-gray-700">"{highlight.sentence}"</p>
										</div>
									{/each}
									{#if analysis.highlights.length > 3}
										<p class="text-xs text-gray-500">+{analysis.highlights.length - 3} more highlights</p>
									{/if}
								</div>
							</div>
						{/if} -->

						<!-- Footer with metrics -->
						<div class="flex items-center justify-between text-xs text-gray-500 pt-3 border-t border-gray-100">
							<div class="flex space-x-4">
								<span>Tokens: {analysis.total_tokens.toLocaleString()}</span>
								<span>Cost: ${analysis.total_cost.toFixed(6)}</span>
							</div>
							<span>By: {analysis.created_by?.name || 'System'}</span>
						</div>
					</div>
				</TimelineItem>
			{/each}
		</Timeline>

		{#if summaryData.has_more}
			<div class="text-center py-6">
				<button class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
					Load More Analyses
				</button>
			</div>
		{/if}
	{/if}
</div>