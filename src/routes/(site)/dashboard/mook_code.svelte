<script lang="ts">
    import { onMount } from 'svelte';
    import { browser } from '$app/environment';
    import { page } from '$app/stores';

    // Import tab components
    import AgentPerformanceTab from '$src/routes/(site)/dashboard/tabs/AgentPerformanceTab.svelte';
    import ChatPerformanceTab from '$src/routes/(site)/dashboard/tabs/ChatPerformanceTab.svelte';
    import ResponseTimeVolumeTab from '$src/routes/(site)/dashboard/tabs/ResponseTimeVolumeTab.svelte';
    import WorkQualityTab from '$src/routes/(site)/dashboard/tabs/WorkQualityTab.svelte';


    // Define a type for tab items for better type safety and readability
    type TabItem = {
        id: string;
        name: string;
    };

    // --- Tab Definitions ---
    // Main navigation tabs
    const mainTabs: TabItem[] = [
        { id: 'team-performance', name: 'ประสิทธิภาพทีม' },
        { id: 'work-quality', name: 'คุณภาพงาน' }
    ];

    // Sub-tabs for 'Team Performance'
    const teamPerformanceTabs: TabItem[] = [
        { id: 'agent-performance', name: 'ประสิทธิภาพเจ้าหน้าที่' },
        { id: 'chat-performance', name: 'ประสิทธิภาพการตอบกลับ' },
        { id: 'response-time-volume', name: 'เวลาตอบและปริมาณข้อความ' }
    ];

    // Sub-tabs for 'Work Quality'
    const workQualityTabs: TabItem[] = [
        { id: 'work-quality-detail', name: 'คุณภาพงาน' }
    ];

    // --- Component State ---
    let activeMainTab: string; // Stores the ID of the currently active main tab
    let activeSubTab: string;  // Stores the ID of the currently active sub-tab

    /**
     * Initializes tab states based on URL search parameters.
     * Sets default tabs if no valid parameters are found.
     */
    function initializeTabs(): void {
        if (!browser) return; // Ensure this function only runs in the client-side browser environment

        const urlParams = $page.url.searchParams;
        const mainTabFromUrl = urlParams.get('mainTab');
        const subTabFromUrl = urlParams.get('subTab');

        // Determine the active main tab: prioritize URL, otherwise default to the first main tab
        const defaultMainTab = mainTabs[0].id;
        activeMainTab = mainTabs.some(tab => tab.id === mainTabFromUrl)
            ? (mainTabFromUrl as string)
            : defaultMainTab;

        // Determine the relevant sub-tabs based on the active main tab
        const subTabsForCurrentMain = getSubTabsForMainTab(activeMainTab);

        // Determine the active sub tab: prioritize URL, otherwise default to the first available sub tab
        activeSubTab = subTabsForCurrentMain.some(tab => tab.id === subTabFromUrl)
            ? (subTabFromUrl as string)
            : (subTabsForCurrentMain.length > 0 ? subTabsForCurrentMain[0].id : '');

        updateUrl(); // Sync the browser URL with the initialized tab states
    }

    /**
     * Updates the browser's URL with the current active main and sub tabs.
     * Uses history.pushState to prevent a full page reload.
     */
    function updateUrl(): void {
        if (browser) {
            const url = new URL($page.url); // Create a mutable URL object from the current page URL store
            url.searchParams.set('mainTab', activeMainTab);
            url.searchParams.set('subTab', activeSubTab);
            history.pushState({}, '', url); // Update URL without reloading
        }
    }

    /**
     * Handles tab selection, updating the active tabs and the URL.
     * If a main tab changes, the sub-tab automatically resets to the first for that new main tab.
     * @param type - 'main' for main tab, 'sub' for sub tab.
     * @param tabId - The ID of the selected tab.
     */
    function selectTab(type: 'main' | 'sub', tabId: string): void {
        if (type === 'main') {
            activeMainTab = tabId;
            // When the main tab changes, automatically select the first sub-tab of the new main tab
            const newSubTabs = getSubTabsForMainTab(activeMainTab);
            activeSubTab = newSubTabs.length > 0 ? newSubTabs[0].id : '';
        } else { // type === 'sub'
            activeSubTab = tabId;
        }
        updateUrl(); // Reflect the tab change in the URL
    }

    /**
     * Helper function to get the correct list of sub-tabs for a given main tab ID.
     * @param mainTabId - The ID of the main tab.
     * @returns An array of TabItem objects representing the sub-tabs.
     */
    function getSubTabsForMainTab(mainTabId: string): TabItem[] {
        if (mainTabId === 'team-performance') {
            return teamPerformanceTabs;
        } else if (mainTabId === 'work-quality') {
            return workQualityTabs;
        }
        return []; // Return an empty array if mainTabId doesn't match
    }

    // --- Svelte Lifecycle Hooks ---
    // Initialize tabs when the component is mounted to the DOM
    onMount(() => {
        initializeTabs();
    });
</script>

<svelte:head>
    <title>Dashboard</title>
</svelte:head>

<div class="p-6 md:p-10 bg-gray-50 min-h-screen">
    <h1 class="text-3xl font-bold text-gray-800 mb-8">แดชบอร์ด</h1>

    <div class="bg-white rounded-lg shadow-md p-4 mb-6">
        <nav class="flex flex-wrap gap-2 md:gap-4 mb-4 border-b border-gray-200 pb-2">
            {#each mainTabs as tab (tab.id)}
                <button
                    on:click={() => selectTab('main', tab.id)}
                    class="px-5 py-2 rounded-lg font-semibold transition-colors duration-200 text-lg"
                    class:bg-blue-600={activeMainTab === tab.id}
                    class:text-white={activeMainTab === tab.id}
                    class:bg-gray-100={activeMainTab !== tab.id}
                    class:text-gray-700={activeMainTab !== tab.id}
                    class:hover:bg-gray-200={activeMainTab !== tab.id}
                    aria-current={activeMainTab === tab.id ? 'page' : undefined}
                >
                    {tab.name}
                </button>
            {/each}
        </nav>

        <nav class="flex flex-wrap gap-2 md:gap-4 pl-4 pt-2">
            {#if activeMainTab === 'team-performance'}
                {#each teamPerformanceTabs as tab (tab.id)}
                    <button
                        on:click={() => selectTab('sub', tab.id)}
                        class="px-4 py-1 rounded-md font-medium text-base transition-colors duration-200"
                        class:bg-blue-100={activeSubTab === tab.id}
                        class:text-blue-800={activeSubTab === tab.id}
                        class:border-b-2={activeSubTab === tab.id}
                        class:border-blue-500={activeSubTab === tab.id}
                        class:text-gray-600={activeSubTab !== tab.id}
                        class:hover:bg-blue-50={activeSubTab !== tab.id}
                        aria-current={activeSubTab === tab.id ? 'page' : undefined}
                    >
                        {tab.name}
                    </button>
                {/each}
            {:else if activeMainTab === 'work-quality'}
                {#each workQualityTabs as tab (tab.id)}
                    <button
                        on:click={() => selectTab('sub', tab.id)}
                        class="px-4 py-1 rounded-md font-medium text-base transition-colors duration-200"
                        class:bg-blue-100={activeSubTab === tab.id}
                        class:text-blue-800={activeSubTab === tab.id}
                        class:border-b-2={activeSubTab === tab.id}
                        class:border-blue-500={activeSubTab === tab.id}
                        class:text-gray-600={activeSubTab !== tab.id}
                        class:hover:bg-blue-50={activeSubTab !== tab.id}
                        aria-current={activeSubTab === tab.id ? 'page' : undefined}
                    >
                        {tab.name}
                    </button>
                {/each}
            {/if}
        </nav>
    </div>

    <div class="tab-content">
        {#if activeMainTab === 'team-performance'}
            {#if activeSubTab === 'agent-performance'}
                <AgentPerformanceTab />
            {:else if activeSubTab === 'chat-performance'}
                <ChatPerformanceTab />
            {:else if activeSubTab === 'response-time-volume'}
                <ResponseTimeVolumeTab />
            {/if}
        {:else if activeMainTab === 'work-quality'}
            {#if activeSubTab === 'work-quality-detail'}
                <WorkQualityTab />
            {/if}
        {/if}
    </div>
</div>

<style>
    /* No custom styles needed beyond Tailwind classes for this improvement */
</style>