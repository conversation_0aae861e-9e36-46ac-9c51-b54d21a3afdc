<script lang="ts">
    import BarChart from '$lib/components/dashboard/BarChart.svelte';
    import LineChart from '$lib/components/dashboard/LineChart.svelte';
    import ScoreCard from '$lib/components/dashboard/ScoreCard.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';

    // Local state for filters
    let timeRange: string = 'Last 7 Days';

    // --- MOCK DATA FOR WORK QUALITY TAB ---

    // Scorecard data
    let averageCSAT: number = 4.2;
    let averageFirstResponseTimeSeconds: number = 25;

    // Other core metrics
    let firstCallResolutionRate = 85.5;
    let averageResolutionTime = 12.3;
    let averageQualityScore = 4.2;
    let escalatedCases = 15;

    // Utility function to generate daily data for charts
    const generateDailyData = (startValue: number, min: number, max: number, days: number = 7) => {
        const data = [];
        for (let i = 0; i < days; i++) {
            const date = new Date(new Date().setDate(new Date().getDate() - (days - 1 - i)));
            const formattedDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            const value = Math.max(min, Math.min(max, startValue + (Math.random() - 0.5) * 2));
            data.push({ label: formattedDate, value: parseFloat(value.toFixed(1)) });
            startValue = value;
        }
        return data;
    };

    // Agent names for WorkQualityTab's data
    const tempAgentNamesForWorkQuality = ['Agent X', 'Agent Y', 'Agent Z'];

    // FIX START: Ensure 'value' remains a number
    // Define an interface for agentQualityScores for better type safety
    interface AgentQualityScoreItem {
        label: string;
        value: number; // Ensure this is a number
    }

    let agentQualityScores: AgentQualityScoreItem[] = tempAgentNamesForWorkQuality.map(agentName => ({
        label: agentName,
        // The value should be a number. Use parseFloat *after* toFixed, or ensure toFixed is only for display.
        // If you need precision for the number itself, use Math.round(value * 10) / 10 to get one decimal place as a number.
        // Or, simply parseFloat(toFixed(1)) to convert the string back to a number.
        value: parseFloat((Math.random() * (4.9 - 3.5) + 3.5).toFixed(1))
    }));
    // FIX END


    let avgCSAT = generateDailyData(4, 2, 5);
    let avgFirstResponseTime = generateDailyData(10, 8, 15);
    let avgResponseTime = generateDailyData(5, 3, 10);

    // Utility function to generate daily sentiment data
    const generateDailySentiment = (days: number = 7) => {
        const data = [];
        for (let i = 0; i < days; i++) {
            const date = new Date(new Date().setDate(new Date().getDate() - (days - 1 - i)));
            const formattedDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            const positive = Math.floor(Math.random() * 50) + 30;
            const neutral = Math.floor(Math.random() * 20) + 10;
            const negative = Math.floor(Math.random() * 15) + 5;
            data.push({ label: formattedDate, positive, neutral, negative });
        }
        return data;
    };
    let dailySentimentTimeline = generateDailySentiment();

    // Data for Overall Sentiment
    let sentimentData = [
        { label: 'Positive', value: 65 },
        { label: 'Neutral', value: 25 },
        { label: 'Negative', value: 10 }
    ];

    // Mock data for Product Sentiment
    interface ProductSentimentAmountItem {
        product: string;
        positive: number;
        neutral: number;
        negative: number;
    }
    const productSentimentAmounts: ProductSentimentAmountItem[] = [
        { product: 'Product A', positive: 50, neutral: 15, negative: 5 },
        { product: 'Product B', positive: 30, neutral: 10, negative: 3 },
        { product: 'Product C', positive: 45, neutral: 12, negative: 7 },
        { product: 'Product D', positive: 20, neutral: 8, negative: 2 },
    ];


    // Mock data for Agent vs. Chatbot comparison (single bar chart)
    interface AgentChatbotComparisonDataItem {
        type: string; // 'Agent' or 'Chatbot'
        count: number;
    }
    const agentChatbotComparisonData: AgentChatbotComparisonDataItem[] = [
        { type: 'Agent', count: 2343 },
        { type: 'Chatbot', count: 904 },
    ];

    // Mock data for Overall Sentiment with explicit labels
    interface OverallSentimentAmountItem {
        label: 'Positive' | 'Neutral' | 'Negative'; // Explicitly define possible labels
        value: number; // Amount of tickets/interactions
    }
    const overallSentimentAmounts: OverallSentimentAmountItem[] = [
        { label: 'Positive', value: 1500 },
        { label: 'Neutral', value: 700 },
        { label: 'Negative', value: 300 },
    ];

    // Pre-calculate colors for Overall Sentiment based on data
    const overallSentimentColors = overallSentimentAmounts.map(item => {
        if (item.label === 'Positive') return COLORS.green;
        if (item.label === 'Neutral') return COLORS.blue;
        if (item.label === 'Negative') return COLORS.red;
        return COLORS.gray; // Fallback color, though not expected to be hit
    });
    // --- END MOCK DATA ---

    // // Handler for "Download CSV" button click
    // const handleDownloadClick = (chartName: string) => {
    //     console.log(`Download CSV button clicked for: ${chartName}`);
    // };

    // // Handler for "Download Whole Page" button click (visual only)
    // const handleDownloadPageClick = () => {
    //     console.log("Download Whole Page button clicked! (No actual download functionality)");
    // };
</script>

<div class="flex flex-col gap-6 p-4 sm:p-6"> <div class="flex justify-end bg-white rounded-lg shadow-md p-4">
        <div class="flex items-center gap-2 flex-wrap justify-end"> <label for="time-range-filter" class="text-gray-700 font-medium">ช่วงเวลา:</label>
            <select id="time-range-filter" bind:value={timeRange} class="border border-gray-300 rounded-md p-2 text-gray-700 w-full sm:w-auto"> <option value="Last 7 Days">7 วันที่ผ่านมา</option>
                <option value="Last 30 Days">30 วันที่ผ่านมา</option>
                <option value="This Month">เดือนนี้</option>
                <option value="Last Month">เดือนที่แล้ว</option>
                <option value="Custom">กำหนดเอง</option>
            </select>
            {#if timeRange === 'Custom'}
                <input type="date" class="border border-gray-300 rounded-md p-2 w-full sm:w-auto" /> <input type="date" class="border border-gray-300 rounded-md p-2 w-full sm:w-auto" /> {/if}
            <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm ml-0 sm:ml-4 mt-2 sm:mt-0 w-full sm:w-auto" >
                ดาวน์โหลด CSV ทั้งหน้า
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow-md p-6 flex flex-col justify-between">
            <ScoreCard
                title="คะแนนความพึงพอใจเฉลี่ย"
                value={averageCSAT}
                valueColor="text-black-600"
                trendValue={+0.1}
            />
        </div>
        <div class="bg-white rounded-lg shadow-md p-6 flex flex-col justify-between">
            <ScoreCard
                title="เวลาตอบกลับครั้งแรกเฉลี่ย (วินาที)"
                value={averageFirstResponseTimeSeconds}
                valueColor="text-black-600"
                trendValue={-2} trendUnit="%"
            />
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2"> <h2 class="text-xl font-semibold text-gray-700">เวลาตอบกลับเฉลี่ย (วินาที): เจ้าหน้าที่ vs. แชทบอท</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto" >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <div class="w-full h-48">
                <BarChart
                    data={agentChatbotComparisonData}
                    label=""
                    barColor={COLORS.green}
                    chartType="verticalBar"
                    labelKey="type"
                    valueKey="count"
                    showValueLabels={true} />
            </div>
        </div>
    </div>

    <hr/>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2"> <h2 class="text-xl font-semibold text-gray-700">คะแนนความพึงพอใจเฉลี่ย รายวัน</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto" >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <div class="w-full h-64">
                <LineChart
                    data={avgCSAT}
                    chartLabel="CSAT เฉลี่ย"
                    lineColor={COLORS.green}
                    showDataLabels={false}
                />
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2"> <h2 class="text-xl font-semibold text-gray-700">เวลาตอบกลับครั้งแรกเฉลี่ย (วินาที) รายวัน</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto" >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <div class="w-full h-64">
                <LineChart
                    data={avgFirstResponseTime}
                    chartLabel="เวลา (วินาที)"
                    lineColor={COLORS.purple}
                    showDataLabels={false}
                />
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2"> <h2 class="text-xl font-semibold text-gray-700">เวลาตอบกลับเฉลี่ย (วินาที) รายวัน</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto" >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <div class="w-full h-64">
                <LineChart
                    data={avgResponseTime}
                    chartLabel="เวลา (วินาที)"
                    lineColor={COLORS.red}
                    showDataLabels={false}
                />
            </div>
        </div>
    </div>

    <hr/>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2"> <h2 class="text-xl font-semibold text-gray-700">จำนวนความรู้สึกทั้งหมด</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto" >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <div class="w-full h-64">
                <BarChart
                    data={overallSentimentAmounts}
                    label="จำนวนทิกเก็ต"
                    barColor={overallSentimentColors}
                    borderColor={overallSentimentColors}
                    chartType="verticalBar"
                    labelKey="label"
                    valueKey="value"
                    showValueLabels={true}
                />
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2"> <h2 class="text-xl font-semibold text-gray-700">จำนวนความรู้สึก รายวัน</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto" >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <div class="w-full h-64">
                <BarChart
                    data={dailySentimentTimeline}
                    label="จำนวนทิกเก็ต"
                    chartType="stackedVerticalBar"
                    labelKey="label"
                    stackedKeys={['positive', 'neutral', 'negative']}
                    stackedColors={{
                        positive: COLORS.green,
                        neutral: COLORS.blue,
                        negative: COLORS.red
                    }}
                    showValueLabels={true} />
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2"> <h2 class="text-xl font-semibold text-gray-700">จำนวนความรู้สึกของทิกเก็ตที่ปิดแล้ว แยกประเภทเคส</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto" >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <div class="w-full h-64">
                <BarChart
                    data={productSentimentAmounts} label="จำนวนทิกเก็ต"
                    chartType="stackedHorizontalBar"
                    labelKey="product"
                    stackedKeys={['positive', 'neutral', 'negative']}
                    stackedColors={{
                        positive: COLORS.green,
                        neutral: COLORS.blue,
                        negative: COLORS.red
                    }}
                    showValueLabels={true} />
            </div>
        </div>
    </div>
</div>