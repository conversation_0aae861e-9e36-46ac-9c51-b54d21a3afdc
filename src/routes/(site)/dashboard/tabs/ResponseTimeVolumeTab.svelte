<script lang="ts">
    import LineChart from '$lib/components/dashboard/LineChart.svelte';
    import BarChart from '$lib/components/dashboard/BarChart.svelte';
    import ScoreCard from '$lib/components/dashboard/ScoreCard.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';

    // Local state for filters
    let timeRange: string = 'Last 7 Days';

    // Utility functions (kept as they might be used by other parts, e.g., table data generation)
    function getRandomDate(start: Date, end: Date) {
        return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    }

    function calculateTimeDifference(start: Date, end: Date | null = null): string {
        const startDate = start;
        const endDate = end ? end : new Date(); // If no end, use current time
        const diffMs = endDate.getTime() - startDate.getTime();

        const minutes = Math.floor((diffMs / (1000 * 60)) % 60);
        const hours = Math.floor((diffMs / (1000 * 60 * 60)) % 24);
        const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        let result = '';
        if (days > 0) result += `${days}d `;
        if (hours > 0) result += `${hours}h `;
        result += `${minutes}m`;
        return result.trim();
    }

    // Function to generate daily data for charts (kept in case needed for LineChart)
    const generateDailyData = (startValue: number, min: number, max: number, days: number = 7) => {
        const data = [];
        for (let i = 0; i < days; i++) {
            const date = new Date(new Date().setDate(new Date().getDate() - (days - 1 - i)));
            const formattedDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            const value = Math.max(min, Math.min(max, startValue + (Math.random() - 0.5) * 2));
            data.push({ label: formattedDate, value: parseFloat(value.toFixed(1)) });
            startValue = value;
        }
        return data;
    };

    // --- Core Scorecard Data ---
    let totalIncomingMessages = 20;
    let totalIncomingTickets = 9;

    // Daily Incoming Chat Volume (Line Chart)
    interface DailyVolumeDataItem {
        label: string; // Date or specific label like '07/16'
        value: number; // Volume count
    }
    const dailyIncomingChatVolume: DailyVolumeDataItem[] = [
        { label: '07/16', value: 13 },
        { label: '07/18', value: 4 },
        { label: '07/20', value: 2 },
        { label: '07/22', value: 1 }
    ];

    // Total Tickets by Group (Bar Chart)
    interface TicketsByGroupDataItem {
        group: string; // Group name like 'Other Services'
        amount: number;
    }
    const totalTicketsByGroup: TicketsByGroupDataItem[] = [
        { group: 'Other Services', amount: 11 },
        { group: 'Sales', amount: 7 },
        { group: 'Support', amount: 5 },
        { group: 'Billing', amount: 3 },
    ];

    // Incoming Messages by Time Slot (Table)
    interface TimeSlotDataItem {
        timeSlot: string; // e.g., '00:00-01:00'
        mon: number;
        tue: number;
        wed: number;
        thu: number;
        fri: number;
        sat: number;
        sun: number;
    }

    // Helper to generate a random number for mock data
    const getRandomInt = (min: number, max: number) => {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min + 1)) + min;
    };

    const generateTimeSlotData = (startHour: number, endHour: number, min: number, max: number): TimeSlotDataItem => {
        const formatHour = (h: number) => String(h).padStart(2, '0');
        const endHourFormatted = endHour === 0 ? '00' : String(endHour).padStart(2, '0'); // Handle 23:00-00:00 case
        return {
            timeSlot: `${formatHour(startHour)}:00-${endHourFormatted}:00`,
            mon: getRandomInt(min, max),
            tue: getRandomInt(min, max),
            wed: getRandomInt(min, max),
            thu: getRandomInt(min, max),
            fri: getRandomInt(min, max),
            sat: getRandomInt(min, max),
            sun: getRandomInt(min, max),
        };
    };

    let incomingMessagesByTimeSlot: TimeSlotDataItem[] = [];

    // Data for 00:00-06:00 (low traffic)
    for (let i = 0; i < 6; i++) {
        incomingMessagesByTimeSlot.push(generateTimeSlotData(i, i + 1, 0, 3));
    }
    // Data for 06:00-09:00 (morning increase)
    for (let i = 6; i < 9; i++) {
        incomingMessagesByTimeSlot.push(generateTimeSlotData(i, i + 1, 1, 7));
    }
    // Data for 09:00-18:00 (peak hours)
    for (let i = 9; i < 18; i++) {
        incomingMessagesByTimeSlot.push(generateTimeSlotData(i, i + 1, 5, 15));
    }
    // Data for 18:00-22:00 (evening decrease)
    for (let i = 18; i < 22; i++) {
        incomingMessagesByTimeSlot.push(generateTimeSlotData(i, i + 1, 2, 8));
    }
    // Data for 22:00-23:00 (late night)
    incomingMessagesByTimeSlot.push(generateTimeSlotData(22, 23, 1, 5));
    // Data for 23:00-00:00 (very late night)
    incomingMessagesByTimeSlot.push(generateTimeSlotData(23, 0, 0, 2));


    // Sort state for the table
    let currentSort: { [key: string]: { column: string; direction: 'asc' | 'desc' | null } } = {
        incomingMessagesByTimeSlot: { column: 'timeSlot', direction: 'asc' }, // Default sort for this table
    };

    /**
     * Sorts a given array of objects based on a key and updates the sort state.
     * @param dataArray The array to be sorted.
     * @param key The property key to sort by.
     * @param tableName A unique string identifying the table (e.g., 'ticketsTransferred').
     * @returns A new sorted array.
     */
    function sortTable<T>(dataArray: T[], key: keyof T, tableName: string): T[] {
        const currentTableSort = currentSort[tableName] || { column: null, direction: null };
        let newDirection: 'asc' | 'desc' = 'asc';

        if (currentTableSort.column === key) {
            newDirection = currentTableSort.direction === 'asc' ? 'desc' : 'asc';
        }

        // Update the reactive state (important for Svelte to re-render)
        currentSort = { ...currentSort, [tableName]: { column: String(key), direction: newDirection } };

        return [...dataArray].sort((a, b) => {
            const aValue = a[key];
            const bValue = b[key];

            if (typeof aValue === 'string' && typeof bValue === 'string') {
                // Special handling for 'timeSlot' to sort chronologically
                if (key === 'timeSlot') {
                    const parseTime = (timeStr: string) => {
                        const [start, end] = timeStr.split('-').map(s => parseInt(s.split(':')[0]));
                        // Convert to minutes for easier comparison, handling midnight cross-over
                        return start * 60 + (end === 0 ? 24 * 60 : end * 60);
                    };
                    const timeA = parseTime(aValue);
                    const timeB = parseTime(bValue);
                    return newDirection === 'asc' ? timeA - timeB : timeB - timeA;
                }
                return newDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            } else if (typeof aValue === 'number' && typeof bValue === 'number') {
                return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
            }
            return 0; // Fallback for other types or if values are not comparable
        });
    }

    // Function to get CSS class based on message count for color formatting
    function getMessageCountColorClass(count: number): string {
        if (count > 10) {
            return 'bg-green-600 text-white font-bold'; // High traffic
        } else if (count > 5) {
            return 'bg-green-400 text-gray-800'; // Medium traffic
        } else if (count > 0) {
            return 'bg-green-200 text-gray-700'; // Low traffic
        } else {
            return 'bg-gray-100 text-gray-500'; // No messages
        }
    }

//     // Handler for "Download CSV" button click
//     const handleDownloadClick = (chartName: string) => {
//         // Only log to console, no pop-up
//         console.log(`Download CSV button clicked for: ${chartName}`);
//     };

//     // Handler for "Download Whole Page" button click (visual only)
//     const handleDownloadPageClick = () => {
//         console.log("Download Whole Page button clicked! (No actual download functionality)");
//     };
</script>

<!-- Start Page -->
<div class="flex flex-col gap-6">
    <div class="flex flex-wrap items-center justify-end gap-4 bg-white rounded-lg shadow-md p-4">
        <div class="flex items-center gap-2">
            <label for="time-range-filter" class="text-gray-700 font-medium">ช่วงเวลา:</label>
            <select id="time-range-filter" bind:value={timeRange} class="border border-gray-300 rounded-md p-2 text-gray-700">
                <option value="Last 7 Days">7 วันที่ผ่านมา</option>
                <option value="Last 30 Days">30 วันที่ผ่านมา</option>
                <option value="This Month">เดือนนี้</option>
                <option value="Last Month">เดือนที่แล้ว</option>
                <option value="Custom">กำหนดเอง</option>
            </select>
            {#if timeRange === 'Custom'}
                <input type="date" class="border border-gray-300 rounded-md p-2" />
                <input type="date" class="border border-gray-300 rounded-md p-2" />
            {/if}
            <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm ml-4"
            >
                ดาวน์โหลด CSV ทั้งหน้า
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-5">
        <div class="bg-white rounded-lg shadow-md p-6">
            <ScoreCard
                title="ข้อความขาเข้าทั้งหมด"
                value={totalIncomingMessages}
                valueColor="text-black-600"
                trendValue={-10} trendUnit="%"
            />
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <ScoreCard
                title="ทิกเก็ตทั้งหมด"
                value={totalIncomingTickets}
                valueColor="text-black-600"
                trendValue={+16} trendUnit="%"
            />
        </div>

        <div class="lg:col-span-2 bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-700">จำนวนทิกเก็ต รายหมวดหมู่</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm"
                >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <div class="w-full h-64">
                <BarChart
                    data={totalTicketsByGroup}
                    chartType="horizontalBar"
                    labelKey="group"
                    valueKey="amount"
                    label=""
                    barColor={COLORS.green}
                    showValueLabels={true}
                />
            </div>
        </div>
    </div>

    <hr/>

    <div class="lg:col-span-4 bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-700">จำนวนข้อความขาเข้า รายวัน</h2>
            <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm"
            >
                ดาวน์โหลด CSV
            </button>
        </div>
        <div class="w-full h-64">
            <LineChart
                data={dailyIncomingChatVolume}
                chartLabel="Volume"
                lineColor={COLORS.green}
                showDataLabels={true}
            />
        </div>
    </div>

    <hr/>

    <div class="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-700">จำนวนข้อความขาเข้า แต่ละช่วงเวลา</h2>
            <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm"
            >
                ดาวน์โหลด CSV
            </button>
        </div>
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'timeSlot', 'incomingMessagesByTimeSlot')}>
                        ช่วงเวลา
                        {#if currentSort.incomingMessagesByTimeSlot.column === 'timeSlot'}
                            {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'mon', 'incomingMessagesByTimeSlot')}>
                        จันทร์
                        {#if currentSort.incomingMessagesByTimeSlot.column === 'mon'}
                            {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'tue', 'incomingMessagesByTimeSlot')}>
                        อังคาร
                        {#if currentSort.incomingMessagesByTimeSlot.column === 'tue'}
                            {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'wed', 'incomingMessagesByTimeSlot')}>
                        พุธ
                        {#if currentSort.incomingMessagesByTimeSlot.column === 'wed'}
                            {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'thu', 'incomingMessagesByTimeSlot')}>
                        พฤหัสบดี
                        {#if currentSort.incomingMessagesByTimeSlot.column === 'thu'}
                            {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'fri', 'incomingMessagesByTimeSlot')}>
                        ศุกร์
                        {#if currentSort.incomingMessagesByTimeSlot.column === 'fri'}
                            {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'sat', 'incomingMessagesByTimeSlot')}>
                        เสาร์
                        {#if currentSort.incomingMessagesByTimeSlot.column === 'sat'}
                            {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'sun', 'incomingMessagesByTimeSlot')}>
                        อาทิตย์
                        {#if currentSort.incomingMessagesByTimeSlot.column === 'sun'}
                            {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {#each incomingMessagesByTimeSlot as item}
                    <tr>
                        <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.timeSlot}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm {getMessageCountColorClass(item.mon)}">{item.mon}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm {getMessageCountColorClass(item.tue)}">{item.tue}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm {getMessageCountColorClass(item.wed)}">{item.wed}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm {getMessageCountColorClass(item.thu)}">{item.thu}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm {getMessageCountColorClass(item.fri)}">{item.fri}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm {getMessageCountColorClass(item.sat)}">{item.sat}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm {getMessageCountColorClass(item.sun)}">{item.sun}</td>
                    </tr>
                {/each}
            </tbody>
        </table>
    </div>
</div>