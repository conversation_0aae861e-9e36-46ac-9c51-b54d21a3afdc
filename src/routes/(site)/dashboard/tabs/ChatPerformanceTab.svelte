<script lang="ts">
    import BarChart from '$lib/components/dashboard/BarChart.svelte';
    import LineChart from '$lib/components/dashboard/LineChart.svelte';
    import ScoreCard from '$lib/components/dashboard/ScoreCard.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';

    // Local state for filters
    let timeRange: string = 'Last 7 Days';

    // --- MOCK DATA FOR CHAT PERFORMANCE TAB ---

    // Initial Scorecards
    let allTickets = 45;
    let allClosedTickets = 5;
    let chatVolume = 1250;
    let closedRate = 3.2;
    let handlingTimeRate = 91.5;
    let avgResponseTime = 7.5;
    let responseTimeRate = 3.2;
    let avgHandlingTime = 85.5;

    // Function to get a random date for mocking
    function getRandomDate(start: Date, end: Date) {
        return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    }

    // Utility to calculate time difference for tickets
    function calculateTimeDifference(start: Date, end: Date | null = null): string {
        const startDate = start;
        const endDate = end ? end : new Date(); // If no end, use current time
        const diffMs = endDate.getTime() - startDate.getTime();

        const minutes = Math.floor((diffMs / (1000 * 60)) % 60);
        const hours = Math.floor((diffMs / (1000 * 60 * 60)) % 24);
        const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        let result = '';
        if (days > 0) result += `${days}d `;
        if (hours > 0) result += `${hours}h `;
        result += `${minutes}m`;
        return result.trim();
    }

    // Ticket Status Chart (Horizontal Bar Chart)
    interface TicketStatusDataItem {
        status: string;
        amount: number;
    }
    const ticketStatusData: TicketStatusDataItem[] = [
        { status: 'Open', amount: 45 },
        { status: 'Assigned', amount: 30 },
        { status: 'Waiting', amount: 15 },
        { status: 'Pending to Close', amount: 10 },
        { status: 'Closed', amount: 60 }
    ];

    // Unclosed Tickets Table
    interface UnclosedTicket {
        ticketNo: string;
        ticketStatus: string;
        customerName: string;
        priority: 'High' | 'Medium' | 'Low';
        sentiment: 'Positive' | 'Neutral' | 'Negative';
        agentName: string;
        createdDateTime: string;
        currentDateTime: string;
        totalUsedTime: string;
    }

    let mockUnclosedTickets: UnclosedTicket[] = Array.from({ length: 8 }).map((_, i) => {
        const createdDate = getRandomDate(new Date(2025, 0, 1), new Date());
        return {
            ticketNo: `UNC-${1000 + i}`,
            ticketStatus: ['Open', 'Assigned', 'Waiting', 'Pending'][Math.floor(Math.random() * 4)],
            customerName: `Cust-${String.fromCharCode(65 + i)}`,
            priority: ['High', 'Medium', 'Low'][Math.floor(Math.random() * 3)] as 'High' | 'Medium' | 'Low',
            sentiment: ['Positive', 'Neutral', 'Negative'][Math.floor(Math.random() * 3)] as 'Positive' | 'Neutral' | 'Negative',
            agentName: `Agent ${String.fromCharCode(65 + Math.floor(Math.random() * 5))}`,
            createdDateTime: createdDate.toLocaleString(),
            currentDateTime: new Date().toLocaleString(),
            totalUsedTime: calculateTimeDifference(createdDate)
        };
    });

    // Closed Tickets Table
    interface ClosedTicket {
        ticketNo: string;
        ticketStatus: string;
        customerName: string;
        priority: 'High' | 'Medium' | 'Low';
        sentiment: 'Positive' | 'Neutral' | 'Negative';
        agentName: string;
        createdDateTime: string;
        closedDateTime: string;
        totalUsedTime: string;
    }

    let mockClosedTickets: ClosedTicket[] = Array.from({ length: 15 }).map((_, i) => {
        const createdDate = getRandomDate(new Date(2024, 11, 1), new Date(2025, 6, 1));
        const closedDate = getRandomDate(createdDate, new Date());
        return {
            ticketNo: `CLS-${2000 + i}`,
            ticketStatus: 'Closed',
            customerName: `Cust-${String.fromCharCode(75 + i)}`,
            priority: ['High', 'Medium', 'Low'][Math.floor(Math.random() * 3)] as 'High' | 'Medium' | 'Low',
            sentiment: ['Positive', 'Neutral', 'Negative'][Math.floor(Math.random() * 3)] as 'Positive' | 'Neutral' | 'Negative',
            agentName: `Agent ${String.fromCharCode(70 + Math.floor(Math.random() * 5))}`,
            createdDateTime: createdDate.toLocaleString(),
            closedDateTime: closedDate.toLocaleString(),
            totalUsedTime: calculateTimeDifference(createdDate, closedDate)
        };
    });

    // Closed Tickets by Case Type Chart
    interface CaseTypeDataItem {
        caseType: string;
        count: number;
    }
    const closedTicketsByCaseType: CaseTypeDataItem[] = [
        { caseType: 'Billing Inquiry', count: 70 },
        { caseType: 'Technical Support', count: 60 },
        { caseType: 'Product Info', count: 50 },
        { caseType: 'Order Status', count: 40 },
        { caseType: 'Complaint', count: 30 }
    ];

    // Closed Tickets by Sub-Case Type Chart
    interface SubCaseTypeDataItem {
        subCaseType: string;
        count: number;
    }
    const closedTicketsBySubCaseType: SubCaseTypeDataItem[] = [
        { subCaseType: 'Login Issue', count: 30 },
        { subCaseType: 'Payment Failed', count: 25 },
        { subCaseType: 'Delivery Delay', count: 20 },
        { subCaseType: 'Account Update', count: 18 },
        { subCaseType: 'Bug Report', count: 12 }
    ];

    // Closed Tickets Case & Sub-Case Type Table
    interface CaseSubCaseInfo {
        caseType: string;
        subCaseType: string;
        count: number;
    }
    let closedCaseSubCaseTable: CaseSubCaseInfo[] = [
        { caseType: 'Technical Support', subCaseType: 'Login Issue', count: 30 },
        { caseType: 'Technical Support', subCaseType: 'Connectivity', count: 20 },
        { caseType: 'Billing Inquiry', subCaseType: 'Incorrect Charge', count: 25 },
        { caseType: 'Billing Inquiry', subCaseType: 'Refund Status', count: 15 },
        { caseType: 'Product Info', subCaseType: 'Features', count: 18 },
        { caseType: 'Product Info', subCaseType: 'Compatibility', count: 10 },
        { caseType: 'Order Status', subCaseType: 'Tracking', count: 20 },
        { caseType: 'Order Status', subCaseType: 'Cancellation', count: 10 },
        { caseType: 'Complaint', subCaseType: 'Agent Behavior', count: 10 },
        { caseType: 'Complaint', subCaseType: 'Service Quality', count: 8 }
    ];
    // --- END MOCK DATA ---

    // SORTING STATE AND FUNCTIONALITY
    // General sort state for any table
    let currentSort: { [key: string]: { column: string; direction: 'asc' | 'desc' | null } } = {
        unclosedTickets: { column: 'ticketNo', direction: 'asc' }, // Default sort for this table
        closedTickets: { column: 'ticketNo', direction: 'asc' }, // Default sort for this table
        closedCaseSubCaseTable: { column: 'caseType', direction: 'asc' } // Default sort for this table
    };

    /**
     * Sorts a given array of objects based on a key and updates the sort state.
     * @param dataArray The array to be sorted.
     * @param key The property key to sort by.
     * @param tableName A unique string identifying the table (e.g., 'ticketsTransferred').
     * @returns A new sorted array.
     */
    function sortTable<T>(dataArray: T[], key: keyof T, tableName: string): T[] {
        const currentTableSort = currentSort[tableName] || { column: null, direction: null };
        let newDirection: 'asc' | 'desc' = 'asc';

        if (currentTableSort.column === key) {
            newDirection = currentTableSort.direction === 'asc' ? 'desc' : 'asc';
        }

        // Update the reactive state (important for Svelte to re-render)
        currentSort = { ...currentSort, [tableName]: { column: String(key), direction: newDirection } };

        return [...dataArray].sort((a, b) => {
            const aValue = a[key];
            const bValue = b[key];

            // Special handling for priority and sentiment if they have a specific order
            // Otherwise, they will be sorted alphabetically.
            if (key === 'priority') {
                const priorityOrder = { 'High': 3, 'Medium': 2, 'Low': 1 };
                const aP = priorityOrder[aValue as 'High' | 'Medium' | 'Low'];
                const bP = priorityOrder[bValue as 'High' | 'Medium' | 'Low'];
                return newDirection === 'asc' ? aP - bP : bP - aP;
            }
            if (key === 'sentiment') {
                const sentimentOrder = { 'Positive': 3, 'Neutral': 2, 'Negative': 1 };
                const aS = sentimentOrder[aValue as 'Positive' | 'Neutral' | 'Negative'];
                const bS = sentimentOrder[bValue as 'Positive' | 'Neutral' | 'Negative'];
                return newDirection === 'asc' ? aS - bS : bS - aS;
            }

            if (typeof aValue === 'string' && typeof bValue === 'string') {
                // For date strings, parse them for proper chronological sorting
                if (key === 'createdDateTime' || key === 'currentDateTime' || key === 'closedDateTime') {
                    const dateA = new Date(aValue);
                    const dateB = new Date(bValue);
                    return newDirection === 'asc' ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
                }
                // For 'totalUsedTime', it's 'Xd Yh Zm', might need custom parsing for exact sort
                // For now, treat as string, but ideally, you'd sort by raw milliseconds
                if (key === 'totalUsedTime') {
                    // This is a simple string comparison; for true duration sort, you'd need to parse "1d 2h 30m" into a number of minutes/seconds.
                    // For example:
                    // const parseDuration = (str: string) => {
                    //    const daysMatch = str.match(/(\d+)d/);
                    //    const hoursMatch = str.match(/(\d+)h/);
                    //    const minsMatch = str.match(/(\d+)m/);
                    //    let totalMins = 0;
                    //    if (daysMatch) totalMins += parseInt(daysMatch[1]) * 24 * 60;
                    //    if (hoursMatch) totalMins += parseInt(hoursMatch[1]) * 60;
                    //    if (minsMatch) totalMins += parseInt(minsMatch[1]);
                    //    return totalMins;
                    // };
                    // const durationA = parseDuration(aValue);
                    // const durationB = parseDuration(bValue);
                    // return newDirection === 'asc' ? durationA - durationB : durationB - durationA;
                }
                return newDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            } else if (typeof aValue === 'number' && typeof bValue === 'number') {
                return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
            }
            return 0; // Fallback for other types or if values are not comparable
        });
    }

    // // Handler for individual "Download CSV" button clicks (no pop-up)
    // const handleDownloadChartClick = (chartName: string) => {
    //     console.log(`Download CSV button clicked for: ${chartName}`);
    // };

    // // Handler for "Download Whole Page" button click (visual only)
    // const handleDownloadPageClick = () => {
    //     console.log("Download Whole Page button clicked! (No actual download functionality)");
    // };
</script>


<!-- Start Page -->
<div class="flex flex-col gap-6">
    <div class="flex justify-end bg-white rounded-lg shadow-md p-4">
        <div class="flex items-center gap-2">
            <label for="time-range-filter" class="text-gray-700 font-medium">ช่วงเวลา:</label>
            <select id="time-range-filter" bind:value={timeRange} class="border border-gray-300 rounded-md p-2 text-gray-700">
                <option value="Last 7 Days">7 วันที่ผ่านมา</option>
                <option value="Last 30 Days">30 วันที่ผ่านมา</option>
                <option value="This Month">เดือนนี้</option>
                <option value="Last Month">เดือนที่แล้ว</option>
                <option value="Custom">กำหนดเอง</option>
            </select>
            {#if timeRange === 'Custom'}
                <input type="date" class="border border-gray-300 rounded-md p-2" />
                <input type="date" class="border border-gray-300 rounded-md p-2" />
            {/if}
            <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm ml-4"
            >
                ดาวน์โหลด CSV ทั้งหน้า
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow-md p-6">
            <ScoreCard
                title="ข้อความขาเข้าทั้งหมด"
                value={chatVolume}
                valueColor="text-black-600"
                trendValue={+200} trendUnit="%"
            />
        </div>        
        <div class="bg-white rounded-lg shadow-md p-6">
            <ScoreCard
                title="ทิกเก็ตของเจ้าหน้าที่ทั้งหมด"
                value={allTickets}
                valueColor="text-black-600"
                trendValue={-5} trendUnit="%"
            />
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <ScoreCard
                title="ทิกเก็ตของเจ้าหน้าที่ที่ปิดแล้ว"
                value={allClosedTickets}
                valueColor="text-black-600"
                trendValue={-10} trendUnit="%"
            />
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <ScoreCard
                title="อัตราการปิดทิกเก็ตของเจ้าหน้าที่ เทียบขาเข้า (%)"
                value={closedRate}
                valueColor="text-black-600"
                trendValue={+0.2} trendUnit="%"
            />
        </div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow-md p-6">
            <ScoreCard
                title="เวลาตอบกลับเฉลี่ยของเจ้าหน้าที่ (วินาที)"
                value={avgResponseTime}
                valueColor="text-black-600"
                trendValue={-0.3} trendUnit="%"
            />
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <ScoreCard
                title="อัตราการตอบกลับของเจ้าหน้าที่ ภายใน 6 วินาที (%)"
                value={responseTimeRate}
                valueColor="text-black-600"
                trendValue={-0.1} trendUnit="%"
            />
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <ScoreCard
                title="เวลาจัดการเฉลี่ยของเจ้าหน้าที่ (นาที)"
                value={avgHandlingTime}
                valueColor="text-black-600"
                trendValue={+0.5} trendUnit="%"
            />
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <ScoreCard
                title="อัตราการจัดการของเจ้าหน้าที่ ภายใน 5 นาที (%)"
                value={handlingTimeRate}
                valueColor="text-black-600"
                trendValue={+1.5} trendUnit="%"
            />
        </div>
    </div>

    <hr/>

    <div class="bg-white p-6 rounded-lg shadow-md">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-700">จำนวนทิกเก็ตของเจ้าหน้าที่ รายสถานะ</h2>
            <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm"
            >
                ดาวน์โหลด CSV
            </button>
        </div>
        <div class="w-full h-[28rem]"> 
            <BarChart
                data={ticketStatusData}
                chartType="horizontalBar"
                labelKey="status"
                valueKey="amount"
                label=""
                barColor={COLORS.purple}
                showValueLabels={true}
            />
        </div>
    </div>

    <hr/>

    <div class="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-700">ทิกเก็ตที่ยังไม่ปิด: ค้างมากกว่า 1 วัน</h2>
            <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm"
            >
                ดาวน์โหลด CSV
            </button>
        </div>
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockUnclosedTickets = sortTable(mockUnclosedTickets, 'ticketNo', 'unclosedTickets')}>
                        เลขทิกเก็ต
                        {#if currentSort.unclosedTickets.column === 'ticketNo'}
                            {#if currentSort.unclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockUnclosedTickets = sortTable(mockUnclosedTickets, 'ticketStatus', 'unclosedTickets')}>
                        สถานะ
                        {#if currentSort.unclosedTickets.column === 'ticketStatus'}
                            {#if currentSort.unclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockUnclosedTickets = sortTable(mockUnclosedTickets, 'customerName', 'unclosedTickets')}>
                        ลูกค้า
                        {#if currentSort.unclosedTickets.column === 'customerName'}
                            {#if currentSort.unclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockUnclosedTickets = sortTable(mockUnclosedTickets, 'priority', 'unclosedTickets')}>
                        ความสำคัญ
                        {#if currentSort.unclosedTickets.column === 'priority'}
                            {#if currentSort.unclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockUnclosedTickets = sortTable(mockUnclosedTickets, 'sentiment', 'unclosedTickets')}>
                        ความรู้สึก
                        {#if currentSort.unclosedTickets.column === 'sentiment'}
                            {#if currentSort.unclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockUnclosedTickets = sortTable(mockUnclosedTickets, 'agentName', 'unclosedTickets')}>
                        เจ้าหน้าที่
                        {#if currentSort.unclosedTickets.column === 'agentName'}
                            {#if currentSort.unclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockUnclosedTickets = sortTable(mockUnclosedTickets, 'createdDateTime', 'unclosedTickets')}>
                        เวลาที่สร้าง
                        {#if currentSort.unclosedTickets.column === 'createdDateTime'}
                            {#if currentSort.unclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockUnclosedTickets = sortTable(mockUnclosedTickets, 'currentDateTime', 'unclosedTickets')}>
                        เวลาปัจจุบัน
                        {#if currentSort.unclosedTickets.column === 'currentDateTime'}
                            {#if currentSort.unclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockUnclosedTickets = sortTable(mockUnclosedTickets, 'totalUsedTime', 'unclosedTickets')}>
                        เวลาที่ใช้ทั้งหมด
                        {#if currentSort.unclosedTickets.column === 'totalUsedTime'}
                            {#if currentSort.unclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {#each mockUnclosedTickets as ticket (ticket.ticketNo)}
                    <tr>
                        <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{ticket.ticketNo}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.ticketStatus}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.customerName}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                {ticket.priority === 'High' ? 'bg-red-100 text-red-800' :
                                ticket.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-green-100 text-green-800'}">
                                {ticket.priority}
                            </span>
                        </td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.sentiment}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.agentName}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.createdDateTime}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.currentDateTime}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.totalUsedTime}</td>
                    </tr>
                {/each}
            </tbody>
        </table>
    </div>

    <hr/>

    <div class="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-700">ทิกเก็ตที่ปิดแล้ว: ค้างมากกว่า 1 วัน</h2>
            <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm"
            >
                ดาวน์โหลด CSV
            </button>
        </div>
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockClosedTickets = sortTable(mockClosedTickets, 'ticketNo', 'closedTickets')}>
                        เลขทิกเก็ต
                        {#if currentSort.closedTickets.column === 'ticketNo'}
                            {#if currentSort.closedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockClosedTickets = sortTable(mockClosedTickets, 'ticketStatus', 'closedTickets')}>
                        สถานะ
                        {#if currentSort.closedTickets.column === 'ticketStatus'}
                            {#if currentSort.closedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockClosedTickets = sortTable(mockClosedTickets, 'customerName', 'closedTickets')}>
                        ลูกค้า
                        {#if currentSort.closedTickets.column === 'customerName'}
                            {#if currentSort.closedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockClosedTickets = sortTable(mockClosedTickets, 'priority', 'closedTickets')}>
                        ความสำคัญ
                        {#if currentSort.closedTickets.column === 'priority'}
                            {#if currentSort.closedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockClosedTickets = sortTable(mockClosedTickets, 'sentiment', 'closedTickets')}>
                        ความรู้สึก
                        {#if currentSort.closedTickets.column === 'sentiment'}
                            {#if currentSort.closedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockClosedTickets = sortTable(mockClosedTickets, 'agentName', 'closedTickets')}>
                        เจ้าหน้าที่
                        {#if currentSort.closedTickets.column === 'agentName'}
                            {#if currentSort.closedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockClosedTickets = sortTable(mockClosedTickets, 'createdDateTime', 'closedTickets')}>
                        เวลาที่สร้าง
                        {#if currentSort.closedTickets.column === 'createdDateTime'}
                            {#if currentSort.closedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockClosedTickets = sortTable(mockClosedTickets, 'closedDateTime', 'closedTickets')}>
                        เวลาที่ปิด
                        {#if currentSort.closedTickets.column === 'closedDateTime'}
                            {#if currentSort.closedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                    <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                        on:click={() => mockClosedTickets = sortTable(mockClosedTickets, 'totalUsedTime', 'closedTickets')}>
                        เวลาที่ใช้ทั้งหมด
                        {#if currentSort.closedTickets.column === 'totalUsedTime'}
                            {#if currentSort.closedTickets.direction === 'asc'}▲{:else}▼{/if}
                        {/if}
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {#each mockClosedTickets as ticket (ticket.ticketNo)}
                    <tr>
                        <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{ticket.ticketNo}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.ticketStatus}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.customerName}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                {ticket.priority === 'High' ? 'bg-red-100 text-red-800' :
                                ticket.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-green-100 text-green-800'}">
                                {ticket.priority}
                            </span>
                        </td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.sentiment}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.agentName}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.createdDateTime}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.closedDateTime}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{ticket.totalUsedTime}</td>
                    </tr>
                {/each}
            </tbody>
        </table>
    </div>

    <hr/>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow-md">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-700">ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่: ประเภทเคส</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm"
                >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <div class="w-full h-[28rem]">
                <BarChart
                    data={closedTicketsByCaseType}
                    chartType="horizontalBar"
                    labelKey="caseType"
                    valueKey="count"
                    label=""
                    barColor={COLORS.green}
                    showValueLabels={true} />
            </div>
        </div>

        <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow-md row-span-2 overflow-y-auto">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-700">ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่: ประเภทเคสและหัวข้อย่อย</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm"
                >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'caseType', 'closedCaseSubCaseTable')}>
                            ประเภทเคส
                            {#if currentSort.closedCaseSubCaseTable.column === 'caseType'}
                                {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                            {/if}
                        </th>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'subCaseType', 'closedCaseSubCaseTable')}>
                            หัวข้อเคสย่อย
                            {#if currentSort.closedCaseSubCaseTable.column === 'subCaseType'}
                                {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                            {/if}
                        </th>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'count', 'closedCaseSubCaseTable')}>
                            จำนวน
                            {#if currentSort.closedCaseSubCaseTable.column === 'count'}
                                {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                            {/if}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {#each closedCaseSubCaseTable as item (item.caseType + item.subCaseType)}
                        <tr>
                            <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.caseType}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.subCaseType}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.count}</td>
                        </tr>
                    {/each}
                </tbody>
            </table>
        </div>

        <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow-md">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-700">ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่: หัวข้อเคสย่อย</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm"
                >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <div class="w-full h-[28rem]">
                <BarChart
                    data={closedTicketsBySubCaseType}
                    chartType="horizontalBar"
                    labelKey="subCaseType"
                    valueKey="count"
                    label=""
                    barColor={COLORS.darkBlue}
                    showValueLabels={true} />
            </div>
        </div>
    </div>
</div>