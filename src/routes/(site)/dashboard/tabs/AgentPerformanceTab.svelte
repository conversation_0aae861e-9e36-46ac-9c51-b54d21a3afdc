<script lang="ts">
    import BarChart from '$lib/components/dashboard/BarChart.svelte';
    import LineChart from '$lib/components/dashboard/LineChart.svelte';
    import ScoreCard from '$lib/components/dashboard/ScoreCard.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';

    // Local state for filters
    let selectedAgent: string = 'All Agents';
    let timeRange: string = 'Last 7 Days';

    // --- MOCK DATA FOR AGENT PERFORMANCE TAB ---
    const generateAgentNames = (count: number) => {
        const names = [];
        for (let i = 1; i <= count; i++) {
            names.push(`Agent ${String.fromCharCode(64 + i)}`); // Agent A, Agent B, etc.
        }
        return names;
    };
    const AGENT_NAMES = generateAgentNames(5); // Example: 5 agents

    const getRandom = (min: number, max: number) => Math.random() * (max - min) + min;
    const getRandomInt = (min: number, max: number) => Math.floor(getRandom(min, max));

    // Helper to generate a random percentage change
    const getRandomPercentageChange = () => parseFloat(getRandom(-10, 10).toFixed(1));

    // 1. Agent Performance Metrics (for grouped bar chart)
    let agentPerformanceMetrics = AGENT_NAMES.map(agentName => ({
        agentName: agentName,
        responseTime: parseFloat(getRandom(0.5, 3).toFixed(1)),
        handlingTime: parseFloat(getRandom(5, 15).toFixed(1)),
        csatScore: parseFloat(getRandom(3, 5).toFixed(1))
    }));

    // 2. Tickets Transferred to Others Table
    let ticketsTransferred = AGENT_NAMES.map(agentName => ({
        agentName: agentName,
        amount: getRandomInt(5, 20),
        percentageChange: getRandomPercentageChange()
    }));

    // 3. Tickets Received From Others Table
    let ticketsReceived = AGENT_NAMES.map(agentName => ({
        agentName: agentName,
        amount: getRandomInt(3, 15),
        percentageChange: getRandomPercentageChange()
    }));

    // 4. Response Rate in 5 mins Table
    let responseRate5Min = AGENT_NAMES.map(agentName => ({
        agentName: agentName,
        responsePercentage: parseFloat(getRandom(70, 99).toFixed(1)),
        percentageChange: getRandomPercentageChange()
    }));

    // Define the interface for Agent Overall Performance items
    interface AgentOverallPerformanceItem {
        agentName: string;
        amountOfClosedTickets: number;
        amountOfUnclosedTickets: number;
        averageResponseTime: number;
        averageHandlingTime: number;
        averageCsat: number; // out of 5
    }

    // 5. Agent Overall Performance Table
    let agentOverallPerformance: AgentOverallPerformanceItem[] = AGENT_NAMES.map(agentName => ({
        agentName: agentName,
        amountOfClosedTickets: getRandomInt(80, 200),
        amountOfUnclosedTickets: getRandomInt(5, 30),
        averageResponseTime: parseFloat(getRandom(0.5, 3).toFixed(1)),
        averageHandlingTime: parseFloat(getRandom(5, 15).toFixed(1)),
        averageCsat: parseFloat(getRandom(3, 5).toFixed(1)),
    }));

    // Pass the list of agent names to the tab for the filter (now local)
    let agentNames = AGENT_NAMES;
    // --- END MOCK DATA ---

    // SORTING STATE AND FUNCTIONALITY
    // General sort state for any table
    let currentSort: { [key: string]: { column: string; direction: 'asc' | 'desc' | null } } = {
        ticketsTransferred: { column: 'amount', direction: 'desc' },
        ticketsReceived: { column: 'amount', direction: 'desc' },
        responseRate5Min: { column: 'responsePercentage', direction: 'desc' },
        agentOverallPerformance: { column: 'agentName', direction: 'asc' }
    };

    /**
     * Sorts a given array of objects based on a key and updates the sort state.
     * @param dataArray The array to be sorted.
     * @param key The property key to sort by.
     * @param tableName A unique string identifying the table (e.g., 'ticketsTransferred').
     * @returns A new sorted array.
     */
    function sortTable<T>(dataArray: T[], key: keyof T, tableName: string): T[] {
        const currentTableSort = currentSort[tableName] || { column: null, direction: null };
        let newDirection: 'asc' | 'desc' = 'asc';

        if (currentTableSort.column === key) {
            newDirection = currentTableSort.direction === 'asc' ? 'desc' : 'asc';
        }

        // Update the reactive state
        currentSort = { ...currentSort, [tableName]: { column: String(key), direction: newDirection } };

        return [...dataArray].sort((a, b) => {
            const aValue = a[key];
            const bValue = b[key];

            if (typeof aValue === 'string' && typeof bValue === 'string') {
                return newDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            } else if (typeof aValue === 'number' && typeof bValue === 'number') {
                return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
            }
            return 0;
        });
    }

    // Helper function to render percentage change with color
    function formatPercentageChange(value: number): string {
        const sign = value > 0 ? '+' : '';
        const colorClass = value > 0 ? 'text-green-600' : value < 0 ? 'text-red-600' : 'text-gray-500';
        return `<span class="${colorClass}">${sign}${value.toFixed(1)}%</span>`;
    }
</script>

<div class="flex flex-col gap-6 p-4 sm:p-6">
    <div class="flex flex-col md:flex-row md:items-center justify-between gap-4 bg-white rounded-lg shadow-md p-4">
        <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 w-full md:w-auto">
            <label for="agent-filter" class="text-gray-700 font-medium">เจ้าหน้าที่:</label>
            <select id="agent-filter" bind:value={selectedAgent} class="border border-gray-300 rounded-md p-2 text-gray-700 w-full sm:w-auto">
                <option value="All Agents">All Agents</option>
                {#each agentNames as name}
                    <option value={name}>{name}</option>
                {/each}
            </select>
        </div>

        <div class="flex flex-wrap items-center justify-start sm:justify-end gap-2 w-full md:w-auto">
            <label for="time-range-filter" class="text-gray-700 font-medium">ช่วงเวลา:</label>
            <select id="time-range-filter" bind:value={timeRange} class="border border-gray-300 rounded-md p-2 text-gray-700 w-full sm:w-auto">
                <option value="Last 7 Days">7 วันที่ผ่านมา</option>
                <option value="Last 30 Days">30 วันที่ผ่านมา</option>
                <option value="This Month">เดือนนี้</option>
                <option value="Last Month">เดือนที่แล้ว</option>
                <option value="Custom">กำหนดเอง</option>
            </select>
            {#if timeRange === 'Custom'}
                <input type="date" class="border border-gray-300 rounded-md p-2 w-full sm:w-auto" />
                <input type="date" class="border border-gray-300 rounded-md p-2 w-full sm:w-auto" />
            {/if}
            <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm mt-2 sm:mt-0 w-full sm:w-auto md:ml-4"
            >
                ดาวน์โหลด CSV ทั้งหน้า
            </button>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
            <h2 class="text-xl font-semibold text-gray-700">ประสิทธิภาพการทำงาน รายบุคคล</h2>
            <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto"
            >
                ดาวน์โหลด CSV
            </button>
        </div>
        <div class="w-full h-96">
            <BarChart
                data={agentPerformanceMetrics}
                chartType="groupedBar"
                labelKey="agentName"
                groupedKeys={[
                    { key: 'responseTime', label: 'เวลาตอบกลับเฉลี่ย (วินาที)', color: COLORS.blue },
                    { key: 'handlingTime', label: 'เวลาจัดการเฉลี่ย (นาที)', color: COLORS.purple },
                    { key: 'csatScore', label: 'ความพึงพอใจเฉลี่ย (เต็ม 5)', color: COLORS.orange }
                ]}
                label=""
                showValueLabels={true} />
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">จำนวนทิกเก็ตที่โอนให้คนอื่น</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto"
                >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'agentName', 'ticketsTransferred')}>
                                เจ้าหน้าที่
                                {#if currentSort.ticketsTransferred.column === 'agentName'}
                                    {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'amount', 'ticketsTransferred')}>
                                จำนวนที่โอนออก
                                {#if currentSort.ticketsTransferred.column === 'amount'}
                                    {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'percentageChange', 'ticketsTransferred')}>
                                % เทียบกับช่วงก่อนหน้า
                                {#if currentSort.ticketsTransferred.column === 'percentageChange'}
                                    {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each ticketsTransferred as item}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center">{item.agentName}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amount}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                    <span class="{item.percentageChange > 0 ? 'text-green-600' : item.percentageChange < 0 ? 'text-red-600' : 'text-gray-500'}">
                                        {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                    </span>
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">จำนวนทิกเก็ตที่คนอื่นโอนให้</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto"
                >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => ticketsReceived = sortTable(ticketsReceived, 'agentName', 'ticketsReceived')}>
                                เจ้าหน้าที่
                                {#if currentSort.ticketsReceived.column === 'agentName'}
                                    {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => ticketsReceived = sortTable(ticketsReceived, 'amount', 'ticketsReceived')}>
                                จำนวนที่ได้รับ
                                {#if currentSort.ticketsReceived.column === 'amount'}
                                    {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => ticketsReceived = sortTable(ticketsReceived, 'percentageChange', 'ticketsReceived')}>
                                % เทียบกับช่วงก่อนหน้า
                                {#if currentSort.ticketsReceived.column === 'percentageChange'}
                                    {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each ticketsReceived as item}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center">{item.agentName}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amount}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                    <span class="{item.percentageChange > 0 ? 'text-green-600' : item.percentageChange < 0 ? 'text-red-600' : 'text-gray-500'}">
                                        {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                    </span>
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">อัตราการตอบกลับภายใน 5 นาที (%)</h2>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto"
                >
                    ดาวน์โหลด CSV
                </button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => responseRate5Min = sortTable(responseRate5Min, 'agentName', 'responseRate5Min')}>
                                เจ้าหน้าที่
                                {#if currentSort.responseRate5Min.column === 'agentName'}
                                    {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => responseRate5Min = sortTable(responseRate5Min, 'responsePercentage', 'responseRate5Min')}>
                                % ตอบกลับภายใน 5 นาที
                                {#if currentSort.responseRate5Min.column === 'responsePercentage'}
                                    {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => responseRate5Min = sortTable(responseRate5Min, 'percentageChange', 'responseRate5Min')}>
                                % เทียบกับช่วงก่อนหน้า
                                {#if currentSort.responseRate5Min.column === 'percentageChange'}
                                    {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each responseRate5Min as item}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center">{item.agentName}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.responsePercentage.toFixed(1)}%</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                    <span class="{item.percentageChange > 0 ? 'text-green-600' : item.percentageChange < 0 ? 'text-red-600' : 'text-gray-500'}">
                                        {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                    </span>
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
            <h2 class="text-xl font-semibold text-gray-700">ตารางสรุปประสิทธิภาพรายบุคคล</h2>
            <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto"
            >
                ดาวน์โหลด CSV
            </button>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'agentName', 'agentOverallPerformance')}>
                            เจ้าหน้าที่
                            {#if currentSort.agentOverallPerformance.column === 'agentName'}
                                {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                            {/if}
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'amountOfClosedTickets', 'agentOverallPerformance')}>
                            จำนวนทิกเก็ตที่ปิดแล้ว
                            {#if currentSort.agentOverallPerformance.column === 'amountOfClosedTickets'}
                                {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                            {/if}
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'amountOfUnclosedTickets', 'agentOverallPerformance')}>
                            จำนวนทิกเก็ตที่ยังไม่ปิด
                            {#if currentSort.agentOverallPerformance.column === 'amountOfUnclosedTickets'}
                                {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                            {/if}
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageResponseTime', 'agentOverallPerformance')}>
                            เวลาตอบกลับเฉลี่ย (วินาที)
                            {#if currentSort.agentOverallPerformance.column === 'averageResponseTime'}
                                {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                            {/if}
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageHandlingTime', 'agentOverallPerformance')}>
                            เวลาจัดการเฉลี่ย (นาที)
                            {#if currentSort.agentOverallPerformance.column === 'averageHandlingTime'}
                                {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                            {/if}
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageCsat', 'agentOverallPerformance')}>
                            CSAT เฉลี่ย (เต็ม 5)
                            {#if currentSort.agentOverallPerformance.column === 'averageCsat'}
                                {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                            {/if}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {#each agentOverallPerformance as item}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center">{item.agentName}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amountOfClosedTickets}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amountOfUnclosedTickets}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageResponseTime.toFixed(1)}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageHandlingTime.toFixed(1)}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageCsat.toFixed(1)}</td>
                        </tr>
                    {/each}
                </tbody>
            </table>
        </div>
    </div>
</div>