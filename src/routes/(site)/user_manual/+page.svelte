<script lang="ts">
    import { t } from '$src/lib/stores/i18n';
    import { 
        Sidebar, 
        SidebarGroup, 
        SidebarWrapper, 
        SidebarDropdownItem, 
        SidebarDropdownWrapper 
    } from "flowbite-svelte";

    let selected = '';
    let searchTerm = '';
    
    let componentMap = {
        // Login
        login_account: () => import('$lib/components/user_manual/LoginAccount.svelte'),
        side_bar_menu: () => import('$lib/components/user_manual/SideBarMenu.svelte'),
        create_account: () => import('$lib/components/user_manual/CreateAccount.svelte'),

        // Settings > General
        site_settings: () => import('$lib/components/user_manual/SiteSettings.svelte'),
        survey_upload: () => import('$lib/components/user_manual/SurveyUpload.svelte'),
        company_info: () => import('$lib/components/user_manual/CompanyInfo.svelte'),
        webhook_connect: () => import('$lib/components/user_manual/WebhookConnect.svelte'),
        ai_chatbot_settings: () => import('$lib/components/user_manual/AIChatbotSettings.svelte'),

        // Settings > Team Management
        partner_settings: () => import('$lib/components/user_manual/PartnerSettings.svelte'),
        department_settings: () => import('$lib/components/user_manual/DepartmentSettings.svelte'),
        staff_tags: () => import('$lib/components/user_manual/StaffTags.svelte'),
        customer_tags: () => import('$lib/components/user_manual/CustomerTags.svelte'),
        ticket_transfer: () => import('$lib/components/user_manual/TicketTransfer.svelte'),
        business_hours: () => import('$lib/components/user_manual/BusinessHours.svelte'),

        // Settings > Accounts
        update_profile: () => import('$lib/components/user_manual/UpdateProfile.svelte'),
        set_self_schedule: () => import('$lib/components/user_manual/SetSelfSchedule.svelte'),

        // Users
        edit_profile: () => import('$lib/components/user_manual/EditProfile.svelte'),
        set_roles: () => import('$lib/components/user_manual/SetRoles.svelte'),
        manage_partner: () => import('$lib/components/user_manual/ManagePartner.svelte'),
        set_department: () => import('$lib/components/user_manual/SetDepartments.svelte'),
        set_tag: () => import('$lib/components/user_manual/SetTags.svelte'),
        // set_work_shift: () => import('$lib/components/user_manual/SetWorkShift.svelte'),

        // Chat-center
        chat_with_customer: () => import('$lib/components/user_manual/ChatWithCustomer.svelte'),
        transfer_ticket: () => import('$lib/components/user_manual/TransferTicket.svelte'),
        close_ticket: () => import('$lib/components/user_manual/CloseTicket.svelte'),
        reopen_ticket: () => import('$lib/components/user_manual/ReopenTicket.svelte'),
        change_priority: () => import('$lib/components/user_manual/ChangePriority.svelte'),
        edit_customer_info: () => import('$lib/components/user_manual/EditCustomerInfo.svelte'),
        edit_customer_tags: () => import('$lib/components/user_manual/EditCustomerTags.svelte'),
        add_customer_note: () => import('$lib/components/user_manual/AddCustomerNote.svelte'),

        // Customers
        edit_customer: () => import('$lib/components/user_manual/EditCustomer.svelte'),
        add_note_from_customer: () => import('$lib/components/user_manual/AddNoteFromCustomer.svelte'),

        // Tickets
        ticket_transfer_repeat: () => import('$lib/components/user_manual/TicketTransferRepeat.svelte'),
        ticket_close_repeat: () => import('$lib/components/user_manual/TicketCloseRepeat.svelte'),
        reopen_ticket_repeat: () => import('$lib/components/user_manual/ReopenTicketRepeat.svelte'),
        priority_change_repeat: () => import('$lib/components/user_manual/PriorityChangeRepeat.svelte'),

        // Knowledge Base
        upload_doc: () => import('$lib/components/user_manual/UploadDoc.svelte'),
        download_doc: () => import('$lib/components/user_manual/DownloadDoc.svelte'),
        delete_doc: () => import('$lib/components/user_manual/DeleteDoc.svelte'),

        // Dashboard
        team_performance: () => import('$lib/components/user_manual/TeamPerformance.svelte'),
        quality_dashboard: () => import('$lib/components/user_manual/QualityDashboard.svelte'),
        appendix_customer_support: () => import('$src/lib/components/user_manual/AppendixCustomerSupport.svelte'),
        appendix_promotion: () => import('$lib/components/user_manual/AppendixPromotion.svelte'),
        appendix_product: () => import('$lib/components/user_manual/AppendixProduct.svelte')
    };

    // Menu structure for filtering
    const menuStructure = [
        {
            id: 'login',
            title: 'การเข้าใช้งาน',
            items: [
                { key: 'login_account', label: '• การเข้าสู่ระบบ' },
                { key: 'side_bar_menu', label: '• แนะนำเมนูต่างๆในระบบ' },
                { key: 'create_account', label: '• วิธีการสร้างบัญชีเพิ่ม' }
            ]
        },
        {
            id: 'settings',
            title: 'การตั้งค่าระบบ',
            subGroups: [
                {
                    id: 'general',
                    title: '• การตั้งค่าทั่วไป',
                    items: [
                        { key: 'site_settings', label: '• วิธีการตั้งค่ารูปแบบเว็บไซต์' },
                        { key: 'survey_upload', label: '• วิธีการอัปโหลดรูปแบบสำรวจความพึงพอใจ' },
                        { key: 'company_info', label: '• วิธีการตั้งค่าข้อมูลบริษัท' },
                        { key: 'webhook_connect', label: '• วิธีการเชื่อมต่อ Webhook สำหรับแพลตฟอร์มโซเชียลมีเดีย' },
                        { key: 'ai_chatbot_settings', label: '• วิธีการตั้งค่าแชทบอทเอไอ' }
                    ]
                },
                {
                    id: 'team',
                    title: '• การจัดการทีม',
                    items: [
                        { key: 'partner_settings', label: '• วิธีการจัดการพาร์ทเนอร์' },
                        { key: 'department_settings', label: '• วิธีการตั้งค่าแผนก' },
                        { key: 'staff_tags', label: '• วิธีการจัดการแท็กเฉพาะทางของพนักงาน' },
                        { key: 'customer_tags', label: '• วิธีการจัดการแท็กของลูกค้า' },
                        { key: 'ticket_transfer', label: '• วิธีการตั้งค่าการโอนทิกเก็ต' },
                        { key: 'business_hours', label: '• วิธีการตั้งค่าเวลาทำการของบริษัท' }
                    ]
                },
                {
                    id: 'accounts',
                    title: '• การใช้หน้าบัญชีผู้ใช้',
                    items: [
                        { key: 'update_profile', label: '• วิธีการอัปเดตข้อมูลส่วนตัว' },
                        { key: 'set_self_schedule', label: '• วิธีการตั้งกะเวลาทำงานของตนเอง' }
                    ]
                }
            ]
        },
        {
            id: 'users',
            title: 'การใช้งานหน้าผู้ใช้',
            items: [
                { key: 'edit_profile', label: '• วิธีการเปลี่ยนหรือแก้ไขประวัติส่วนตัว' },
                { key: 'set_roles', label: '• วิธีการตั้งบทบาทหน้าที่' },
                { key: 'manage_partner', label: '• วิธีการเพิ่มหรือลดพาร์ทเนอร์' },
                { key: 'set_department', label: '• วิธีการปรับเปลี่ยนแผนก' },
                { key: 'set_tag', label: '• วิธีการปรับเปลี่ยนแท็กพนักงาน' },
                // { key: 'set_work_shift', label: '• วิธีการตั้งกะเวลาทำงาน' }
            ]
        },
        {
            id: 'chat',
            title: 'การใช้งานหน้าแชท',
            items: [
                { key: 'chat_with_customer', label: '• วิธีการสนทนากับลูกค้า' },
                { key: 'transfer_ticket', label: '• วิธีการโอนทิกเก็ตให้พนักงานอื่น' },
                { key: 'close_ticket', label: '• วิธีการปิดทิกเก็ต' },
                { key: 'reopen_ticket', label: '• วิธีการกลับมามอบหมายทิกเก็ตที่ถูกปิดไปแล้ว' },
                { key: 'change_priority', label: '• วิธีเปลี่ยนลำดับความสำคัญ' },
                { key: 'edit_customer_info', label: '• วิธีแก้ไขข้อมูลลูกค้า' },
                { key: 'edit_customer_tags', label: '• วิธีการแก้ไขแท็กของลูกค้า' },
                { key: 'add_customer_note', label: '• วิธีเพิ่มโน๊ตของลูกค้า' }
            ]
        },
        {
            id: 'customers',
            title: 'การใช้งานหน้าลูกค้า',
            items: [
                { key: 'edit_customer', label: '• วิธีแก้ไขข้อมูลลูกค้า' },
                { key: 'add_note_from_customer', label: '• วิธีเพิ่มโน๊ตผ่านลูกค้า' }
            ]
        },
        {
            id: 'tickets',
            title: 'การใช้งานหน้าทิกเก็ต',
            items: [
                { key: 'ticket_transfer_repeat', label: '• วิธีการโอนทิกเก็ตให้พนักงานอื่น' },
                { key: 'ticket_close_repeat', label: '• วิธีการปิดทิกเก็ต' },
                { key: 'reopen_ticket_repeat', label: '• วิธีการกลับมามอบหมายทิกเก็ตที่ถูกปิดไปแล้ว' },
                { key: 'priority_change_repeat', label: '• วิธีเปลี่ยนลำดับความสำคัญ' }
            ]
        },
        {
            id: 'documents',
            title: 'การอัปโหลดเอกสาร',
            items: [
                { key: 'upload_doc', label: '• วิธีการอัปโหลดเอกสาร' },
                { key: 'download_doc', label: '• วิธีดาวน์โหลดเอกสาร' },
                { key: 'delete_doc', label: '• วิธีลบเอกสาร' }
            ]
        },
        {
            id: 'dashboard',
            title: 'การดูหน้าแดชบอร์ด',
            items: [
                { key: 'team_performance', label: '• หน้าประสิทธิภาพทีม' },
                { key: 'quality_dashboard', label: '• หน้าคุณภาพงาน' }
            ]
        },
        {
            id: 'appendix',
            title: 'ภาคผนวก',
            items: [
                { key: 'appendix_customer_support', label: '• รายละเอียดตารางการสนับสนุนลูกค้า' },
                { key: 'appendix_promotion', label: '• รายละเอียดโปรโมชั่น' },
                { key: 'appendix_product', label: '• รายละเอียดตารางผลิตภัณฑ์' }
            ]
        }
    ];

    let LoadedComponent = null;

    async function loadComponent(key: string) {
        selected = key;
        if (componentMap[key]) {
            const module = await componentMap[key]();
            LoadedComponent = module.default;
        }
    }

    // Filter function
    function filterMenu(menu, searchTerm) {
        if (!searchTerm) return menu;
        
        const filtered = menu.map(group => {
            const filteredGroup = { ...group };
            
            // Check if group title matches
            const groupMatches = group.title.toLowerCase().includes(searchTerm.toLowerCase());
            
            // Filter items
            if (group.items) {
                if (groupMatches) {
                    // If group title matches, show all items in this group
                    filteredGroup.items = group.items;
                } else {
                    // Otherwise, filter items by search term
                    filteredGroup.items = group.items.filter(item => 
                        item.label.toLowerCase().includes(searchTerm.toLowerCase())
                    );
                }
            }
            
            // Filter subgroups
            if (group.subGroups) {
                filteredGroup.subGroups = group.subGroups.map(subGroup => {
                    const filteredSubGroup = { ...subGroup };
                    const subGroupMatches = subGroup.title.toLowerCase().includes(searchTerm.toLowerCase());
                    
                    if (groupMatches || subGroupMatches) {
                        // If parent group or subgroup title matches, show all items in this subgroup
                        filteredSubGroup.items = subGroup.items;
                    } else {
                        // Otherwise, filter items by search term
                        filteredSubGroup.items = subGroup.items.filter(item => 
                            item.label.toLowerCase().includes(searchTerm.toLowerCase())
                        );
                    }
                    
                    return filteredSubGroup;
                }).filter(subGroup => 
                    subGroup.items.length > 0
                );
            }
            
            return filteredGroup;
        }).filter(group => {
            const hasMatchingItems = group.items && group.items.length > 0;
            const hasMatchingSubGroups = group.subGroups && group.subGroups.length > 0;
            const titleMatches = group.title.toLowerCase().includes(searchTerm.toLowerCase());
            
            return hasMatchingItems || hasMatchingSubGroups || titleMatches;
        });
        
        return filtered;
    }

    $: filteredMenu = filterMenu(menuStructure, searchTerm);
</script>

<svelte:head>
    <title>{t('userManual')}</title>
</svelte:head>

<div class="flex h-screen bg-gray-50"> 
    <div class="w-[270px] overflow-y-auto bg-gray-50 border-r max-h-screen">
        <Sidebar class="min-h-screen bg-gray-50">
            <SidebarWrapper>
                <!-- Search Bar -->
                <div class="p-4 border-b border-gray-200">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <input 
                            type="text" 
                            bind:value={searchTerm}
                            placeholder="ค้นหาเมนู..." 
                            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                        />
                    </div>
                    {#if searchTerm}
                        <button 
                            on:click={() => searchTerm = ''}
                            class="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    {/if}
                </div>

                <SidebarGroup>
                    {#each filteredMenu as group}
                        {#if group.subGroups}
                            <SidebarDropdownWrapper label={group.title} isOpen={true}>
                                {#each group.subGroups as subGroup}
                                    {#if subGroup.items.length > 0}
                                        <SidebarDropdownWrapper label={subGroup.title} isOpen={true}>
                                            {#each subGroup.items as item}
                                                <SidebarDropdownItem 
                                                    label={item.label}
                                                    on:click={() => loadComponent(item.key)}
                                                    active={selected === item.key}
                                                />
                                            {/each}
                                        </SidebarDropdownWrapper>
                                    {/if}
                                {/each}
                            </SidebarDropdownWrapper>
                        {:else}
                            <SidebarDropdownWrapper label={group.title} isOpen={true}>
                                {#each group.items as item}
                                    <SidebarDropdownItem 
                                        label={item.label}
                                        on:click={() => loadComponent(item.key)}
                                        active={selected === item.key}
                                    />
                                {/each}
                            </SidebarDropdownWrapper>
                        {/if}
                    {/each}

                    {#if filteredMenu.length === 0}
                        <div class="p-4 text-center text-gray-500 text-sm">
                            ไม่พบเมนูที่ค้นหา
                        </div>
                    {/if}
                </SidebarGroup>
            </SidebarWrapper>
        </Sidebar>
    </div>

    <div class="flex-1 overflow-y-auto p-10 bg-white max-h-screen">
        {#if LoadedComponent}
            <svelte:component this={LoadedComponent} />
        {:else}
            <p class="text-gray-400 mt-0 self-start">กรุณาเลือกเมนูจากด้านซ้าย</p>
        {/if}
    </div>
</div>