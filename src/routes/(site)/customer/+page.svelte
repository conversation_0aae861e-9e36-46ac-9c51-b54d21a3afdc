<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import { page } from '$app/stores';

	import {
		Table,
		TableHead,
		TableHeadCell,
		TableBody,
		TableBodyRow,
		TableBodyCell,
		Input,
		Tooltip,
		Indicator,
		Breadcrumb,
		BreadcrumbItem,
		Button,
		Dropdown,
		Checkbox,
		Label
	} from 'flowbite-svelte';
	import { PhoneSolid } from 'flowbite-svelte-icons';
	import {
		UsersSolid,
		CaretDownSolid,
		CaretUpSolid,
		SearchOutline,
		ChevronDownOutline,
		AdjustmentsHorizontalSolid
	} from 'flowbite-svelte-icons';
	import type { PageData } from './$types';
	import Pagination from '$src/lib/components/UI/pagination.svelte';
	import { formatTimestamp, displayDate } from '$lib/utils';
	import { getColorClass } from '$lib/utils';
	import { CustomerService } from '$src/lib/api/features/customer/customers.service';
	import { onMount } from 'svelte';
	import type { CustomerInterface } from '$lib/api/types/customer';
	import CustomerDetailSidebar from '$lib/components/UI/CustomerDetailSidebar.svelte';

	export let data: PageData;
	$: ({ customers: initialCustomers, error } = data);

	console.log(initialCustomers);


	// Always use server-side pagination
	let customers: CustomerInterface[] = [];
	let isLoading = false;

	$: role = $page.data.role;
	$: isAgent = role === 'Agent';

	// Sidebar state
	let isSidebarOpen = false;
	let selectedCustomerId: number | null = null;
	let isSidebarLoading = false;

	// Filter state - make these directly reactive instead of nested in an object
	let selectedTags = new Set(['All']);
	let selectedPlatforms = new Set(['All']);

	// Filter options (loaded from API)
	let tagOptions: Array<{ id: number; name: string; color: string }> = [];
	let platformOptions: Array<{ value: string; label: string; color: string }> = [];

	// Available options with proper typing
	$: tagOptionsList = [
		{ name: 'All', id: t('filter_all') },
		...tagOptions.sort((a: any, b: any) => a.name.localeCompare(b.name))
	];

	$: customerMatchedTags = customers?.map((customer) => ({
		id: customer.customer_id,
		tags: customer.tags || [] // user_tags already contains full tag objects
	}));

	$: platformOptionsList = [
		{ value: 'All', label: 'All'},
		...platformOptions.sort((a: any, b: any) => (a.label || '').localeCompare(b.label || ''))
	];

	// For debugging
	// $: {console.warn("+page.svelte: platformOptionsList:", platformOptionsList)}

	function maskPhoneNumber(phone: string): string {
		if (!phone) return '';
		if (isAgent) {
			const len = phone.length;
			if (len <= 4) return phone;
			return phone.slice(0, 3) + 'x'.repeat(len - 5) + phone.slice(len - 2);
		}
		return phone;
	}

	let searchQuery = '';
	let sortColumn: keyof (typeof customers)[0] = 'customer_id';
	let sortDirection: 'asc' | 'desc' = 'asc';

	// Server-side ordering mapping
	const columnFieldMap = {
		customer_id: 'customer_id',
		name: 'name',
		email: 'email',
		platforms: 'first_platform_name' // Backend annotated field for platform sorting
	};

	let searchId = '';
	let searchPhone = '';
	let searchEmail = '';

	function sortBy(column: keyof (typeof customers)[0]) {
		handleServerSort(column);
	}

	// Server-side sorting
	function handleServerSort(column: string) {
		const backendField = columnFieldMap[column];
		if (!backendField) return;

		let ordering = backendField;
		if (sortColumn === column && sortDirection === 'asc') {
			ordering = '-' + backendField;
			sortDirection = 'desc';
		} else {
			sortDirection = 'asc';
		}
		sortColumn = column;

		currentPage = 1;
		fetchCustomers(ordering);
	}

	// Server-side fetch
	const customerService = new CustomerService();

	// async function fetchCustomers(ordering = 'customer_id') {
	// 	isLoading = true;

	// 	// Build filters with better debugging
	// 	const tagFilters = Array.from(selectedTags).filter((t) => t !== 'All');
	// 	const platformFilters = Array.from(selectedPlatforms).filter((p) => p !== 'All');

	// 	const filters = {
	// 		search: searchQuery.trim() || '',
	// 		tags: tagFilters.join(','),
	// 		platforms: platformFilters.join(','),
	// 		page: currentPage,
	// 		limit: itemsPerPage
	// 	};

	// 	console.log('=== FETCH CUSTOMERS DEBUG ===');
	// 	console.log('Selected filter values:');
	// 	console.log('- Selected Tags:', Array.from(selectedTags));
	// 	console.log('- Selected Platforms:', Array.from(selectedPlatforms));
	// 	console.log('- Search Query:', searchQuery);
	// 	console.log('Filters sent to API:', filters);
	// 	console.log('Current ordering:', ordering);
	// 	console.log('Current page:', currentPage);

	// 	try {
	// 		// Use token from server-side data
	// 		const token = data.token || '';

	// 		const response = await customerService.getCustomersWithFiltersAndOrdering(
	// 			token,
	// 			filters,
	// 			ordering
	// 		);

	// 		if (response.res_status === 200) {
	// 			// Handle paginated response structure
	// 			if (response.customers?.results) {
	// 				customers = response.customers.results;
	// 				totalItems = response.customers.count || 0;
	// 				totalPages = Math.ceil(totalItems / itemsPerPage);
	// 			} else {
	// 				// Fallback for direct array response
	// 				customers = Array.isArray(response.customers) ? response.customers : [];
	// 				totalItems = customers.length;
	// 				totalPages = 1;
	// 			}
	// 		} else {
	// 			console.error('Failed to fetch customers:', response.error_msg);
	// 			customers = [];
	// 		}
	// 	} catch (error) {
	// 		console.error('Error fetching customers:', error);
	// 		customers = [];
	// 	}

	// 	isLoading = false;
	// }

	// 4. Update your server-side fetch function to handle platform filtering
	async function fetchCustomers(ordering = 'customer_id') {
		isLoading = true;

		// Build filters with platform types
		const tagFilters = Array.from(selectedTags).filter((t) => t !== 'All');
		const platformFilters = Array.from(selectedPlatforms).filter((p) => p !== 'All');

		const filters = {
			search: searchQuery.trim() || '',
			tags: tagFilters.join(','),
			platforms: platformFilters.join(','), // This should be platform types like 'LINE,FACEBOOK'
			// platforms: 'FACEBOOK', // This should be platform types like 'LINE,FACEBOOK'
			page: currentPage,
			limit: itemsPerPage
		};

		console.log('=== FETCH CUSTOMERS DEBUG ===');
		console.log('Selected filter values:');
		console.log('- Selected Tags:', Array.from(selectedTags));
		console.log('- Selected Platforms:', Array.from(selectedPlatforms));
		console.log('- Search Query:', searchQuery);
		console.log('Filters sent to API:', filters);

		try {
			const token = data.token || '';
			const response = await customerService.getCustomersWithFiltersAndOrdering(
				token,
				filters,
				ordering
			);

			if (response.res_status === 200) {
				if (response.customers?.results) {
					customers = response.customers.results;
					totalItems = response.customers.count || 0;
					totalPages = Math.ceil(totalItems / itemsPerPage);
				} else {
					customers = Array.isArray(response.customers) ? response.customers : [];
					totalItems = customers.length;
					totalPages = 1;
				}
				
				// Reload filter options after getting new data
				await loadFilterOptions();
			} else {
				console.error('Failed to fetch customers:', response.error_msg);
				customers = [];
			}
		} catch (error) {
			console.error('Error fetching customers:', error);
			customers = [];
		}

		isLoading = false;
	}

	// Fetch unique platforms from API instead
	// 5. Helper function to get all unique platform types from current customers
	// function getUniquePlatformTypes(customers) {
	// 	const platforms = new Set();
	// 	customers.forEach(customer => {
	// 		if (customer.platforms) {
	// 			customer.platforms.forEach(platform => {
	// 				platforms.add(platform.platform);
	// 			});
	// 		}
	// 	});
	// 	return Array.from(platforms);
	// }

	// Debounced search
	
	let searchTimeout: ReturnType<typeof setTimeout>;
	function delayedSearch() {
		if (searchTimeout) clearTimeout(searchTimeout);
		searchTimeout = setTimeout(() => {
			currentPage = 1;
			fetchCustomers();
		}, 500);
	}

	$: searchQuery, delayedSearch();

	// function filterAll(item: any, term: string) {
	// 	const t = term.toLowerCase();
	// 	return [
	// 		String(item.customer_id), // ID
	// 		item.name ||
	// 			(item.first_name && item.last_name ? `${item.first_name} ${item.last_name}` : '') ||
	// 			item.line_user?.display_name, // Name
	// 		item.phone, // Phone
	// 		item.platforms?.name, // Platform
	// 		item.email, // Email
	// 		displayDate(item.created_on) // Created On
	// 	]
	// 		.filter(Boolean)
	// 		.some((field) => field.toLowerCase().includes(t));
	// }

	function filterAll(item: any, term: string) {
		const t = term.toLowerCase();
		
		// Handle platforms array safely
		let platformTypes = '';
		let channelNames = [];
		if (Array.isArray(item.platforms)) {
			platformTypes = item.platforms.map(p => p.platform).join(' ');
			channelNames = item.platforms.map(p => p.channel_name || p.name);
		} else if (item.platforms?.name) {
			platformTypes = item.platforms.name;
		}
		
		// Handle tags array safely
		let tagNames = '';
		if (Array.isArray(item.tags)) {
			tagNames = item.tags.map(tag => tag.name).join(' ');
		} else if (item.tag?.name) {
			tagNames = item.tag.name;
		}
		
		// Build comprehensive search array with all searchable fields
		const searchFields = [
			String(item.customer_id), // Customer ID
			item.name, // Name field
			item.first_name, // First name
			item.last_name, // Last name
			item.first_name && item.last_name ? `${item.first_name} ${item.last_name}` : '', // Full name
			item.line_user?.display_name, // LINE display name
			item.phone, // Phone number
			item.email, // Email address
			platformTypes, // All platform types
			tagNames, // All tag names
			...channelNames, // Include channel names from platforms
			displayDate(item.created_on) // Created date (formatted)
		];
		
		return searchFields
			.filter(Boolean) // Remove null/undefined values
			.some((field) => String(field).toLowerCase().includes(t));
	}

	function compare(a: any, b: any) {
		let av = a[sortColumn],
			bv = b[sortColumn];
		if (sortColumn === 'created_on') {
			av = new Date(av).getTime();
			bv = new Date(bv).getTime();
		}
		if (typeof av === 'number' && typeof bv === 'number') {
			return sortDirection === 'asc' ? av - bv : bv - av;
		}
		return sortDirection === 'asc'
			? String(av).localeCompare(String(bv))
			: String(bv).localeCompare(String(av));
	}

	// Calculate statistics
	$: totalCustomers = totalItems || customers?.length || 0;

	//////////////// Pagination Logic ////////////////
	// pagination state variables
	let currentPage = 1;
	let itemsPerPage = 10;
	let totalItems = 0;
	let totalPages = 1;

	// Update pagination calculation
	$: totalPages = Math.ceil(totalItems / itemsPerPage) || 1;


	// Update the client-side filtering logic (if you need it for immediate filtering)
	$: filteredCustomers = customers.filter(customer => {
		// Platform filter
		if (!selectedPlatforms.has('All')) {
			const customerPlatformTypes = customer.platforms?.map(p => p.platform) || [];
			const hasMatchingPlatform = Array.from(selectedPlatforms).some(selectedPlatform => 
				customerPlatformTypes.includes(selectedPlatform)
			);
			if (!hasMatchingPlatform) return false;
		}
		
		// Tag filter
		if (!selectedTags.has('All')) {
			const customerTagNames = customer.tags?.map(t => t.name) || [];
			const hasMatchingTag = Array.from(selectedTags).some(selectedTag => 
				customerTagNames.includes(selectedTag)
			);
			if (!hasMatchingTag) return false;
		}
		
		// Search filter
		if (searchQuery.trim()) {
			return filterAll(customer, searchQuery);
		}
		
		return true;
	});


	// paginatedCustomers
	$: paginatedCustomers = filteredCustomers;

	// $: paginatedCustomers = customers;

	function updateCurrentPage(newCurrentPage: number) {
		currentPage = newCurrentPage;
		fetchCustomers();
	}

	// Filter toggle functions
	function toggleTag(tag: { name: string; id: string | number }) {
		const newSet = new Set(selectedTags);
		const tagName = tag.name;

		if (tagName === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(tagName)) {
				newSet.delete(tagName);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(tagName);
			}
		}

		selectedTags = newSet;
		currentPage = 1;
		fetchCustomers();
	}

	function togglePlatform(platform: { value: string; label: string }) {
		const newSet = new Set(selectedPlatforms);
		const platformName = platform.label;

		if (platformName === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(platformName)) {
				newSet.delete(platformName);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(platformName);
			}
		}

		selectedPlatforms = newSet;
		currentPage = 1;
		fetchCustomers();
	}

	// Sidebar functions
	function openSidebar(customerId: number) {
		selectedCustomerId = customerId;
		isSidebarOpen = true;
	}

	async function closeSidebar() {
		// Prevent closing if sidebar is loading
		if (isSidebarLoading) return;

		isSidebarOpen = false;
		selectedCustomerId = null;
		// Refresh customer list to ensure data consistency
		await fetchCustomers();
	}

	// Handle loading state changes from sidebar
	function handleSidebarLoadingStateChanged(event) {
		isSidebarLoading = event.detail.isLoading;
	}

	function resetFilters() {
		selectedTags = new Set(['All']);
		selectedPlatforms = new Set(['All']);
		searchQuery = '';
		currentPage = 1;
		fetchCustomers();
	}

	// Load filter options
	// async function loadFilterOptions() {
	// 	try {
	// 		const token = data.token || '';

	// 		// Load tags
	// 		const tagsResponse = await customerService.getFilterTags(token);
	// 		if (tagsResponse.res_status === 200) {
	// 			tagOptions = tagsResponse.data;
	// 		}

	// 		// Load platforms
	// 		const platformsResponse = await customerService.getFilterPlatforms(token);
	// 		if (platformsResponse.res_status === 200) {
	// 			platformOptions = platformsResponse.data;
	// 		}
	// 	} catch (error) {
	// 		console.error('Error loading filter options:', error);
	// 	}
	// }

	async function loadFilterOptions() {
		try {
			const token = data.token || '';

			// Load tags
			const tagsResponse = await customerService.getFilterTags(token);
			if (tagsResponse.res_status === 200) {
				tagOptions = tagsResponse.data;
			}

			// Load platforms - get unique platform types from customers
			const platformsResponse = await customerService.getFilterPlatforms(token);
				if (platformsResponse.res_status === 200) {
					platformOptions = platformsResponse.data;
				}
			
			// if (customers && customers.length > 0) {
			// 	const uniquePlatforms = new Set();
			// 	customers.forEach(customer => {
			// 		if (customer.platforms) {
			// 			customer.platforms.forEach(platform => {
			// 				uniquePlatforms.add(platform.platform);
			// 			});
			// 		}
			// 	});
			// 	platformOptions = Array.from(uniquePlatforms).map(platform => ({
			// 		id: platform,
			// 		name: platform,
			// 		color: '' // You can add color logic if needed
			// 	}));
			// } else {
			// 	// Fallback: load from API if available
			// 	const platformsResponse = await customerService.getFilterPlatforms(token);
			// 	if (platformsResponse.res_status === 200) {
			// 		platformOptions = platformsResponse.data;
			// 	}
			// }
		} catch (error) {
			console.error('Error loading filter options:', error);
		}
	}


	// Format tag name to capitalize first letter
	// TODO - I remember I use this filter explicitly once in the code, but I cannot find it
	// Use this function instead of inline formatting if found
	function formatTagName(tag: string): string {
		// return tag.charAt(0).toUpperCase() + tag.slice(1);
		return tag.charAt(0).toUpperCase() + tag.slice(1).split('_').join(' ');
	}

	// Initialize
	onMount(() => {
		loadFilterOptions();
		fetchCustomers();
	});
</script>

<svelte:head>
	<title>{t('customers')}</title>
</svelte:head>

<div id="customer-page-container" class="relative flex h-screen">
	<!-- Overlay for dimming effect when sidebar is open -->
	{#if isSidebarOpen}
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<!-- svelte-ignore a11y-no-static-element-interactions -->
		<div
			id="customer-page-overlay"
			class="fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300 {isSidebarLoading ? 'cursor-not-allowed' : 'cursor-pointer'}"
			on:click={closeSidebar}
		></div>
	{/if}

	<div id="customer-page-content" class="w-full overflow-x-auto overflow-y-auto bg-white p-8 transition-all duration-300 {isSidebarOpen ? 'md:mr-1/2' : ''}"
	>
		<Breadcrumb id="customer-page-breadcrumb" aria-label="Default breadcrumb example" class="mb-3">
			<BreadcrumbItem id="customer-page-breadcrumb-home" href="/" home>
				<span class="text-gray-400">{t('home')}</span>
			</BreadcrumbItem>
			<BreadcrumbItem id="customer-page-breadcrumb-customers">
				<span class="text-gray-700">{t('customers')}</span>
			</BreadcrumbItem>
		</Breadcrumb>

		<div id="customer-page-header" class="mb-6">
			<h2 id="customer-page-title" class="text-2xl font-bold">{t('customers')}</h2>
			<p id="customer-page-count" class="text-gray-600">{totalCustomers} {t('customers')}</p>
		</div>

		<!-- Filters and Search Bar - Improved Layout -->
		<div
			id="customer-page-filters-container"
			class="mb-6 flex flex-col items-start gap-4 lg:flex-row lg:items-center lg:justify-between"
		>
			<!-- Left side - Filter Buttons -->
			<div id="customer-page-filter-buttons" class="flex flex-wrap gap-3">
				<!-- Tag Filter -->
				<div>
					<Button
						id="customer-page-tag-filter-button"
						color={!selectedTags.has('All') ? 'dark' : 'none'}
						class={`${!selectedTags.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_tag')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown id="customer-page-tag-filter-dropdown" class="max-h-80 w-72 overflow-y-auto p-2 shadow-lg">
						{#each tagOptionsList as tag}
							<!-- <div class="flex items-center justify-between rounded p-2 hover:bg-gray-100"> -->
							<Label class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100">
								<Checkbox
								id="customer-page-tag-checkbox-{tag.id}"
								checked={selectedTags.has(tag.name)}
								on:change={() => toggleTag(tag)}
								class="text-gray-700 focus:ring-gray-700 p-2"
								inline
								/>
								<span class="ml-2 text-sm">
									{tag.name === 'All' ? t('filter_all') : formatTagName(tag.name)}
								</span>
							<!-- </Checkbox> -->
						<!-- </div> -->
							</Label>
						{/each}
					</Dropdown>
				</div>

				<!-- Platform Filter -->
				<div>
					<Button
						id="customer-page-platform-filter-button"
						color={!selectedPlatforms.has('All') ? 'dark' : 'none'}
						class={`${!selectedPlatforms.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_platform')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown id="customer-page-platform-filter-dropdown" class="max-h-80 w-72 overflow-y-auto p-2 shadow-lg">
						{#each platformOptionsList as platform}
							<Label class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100">
								<Checkbox
									id="customer-page-platform-checkbox-{platform.value}"
									checked={selectedPlatforms.has(platform.label)}
									on:change={() => togglePlatform(platform)}
									class="text-gray-700 focus:ring-gray-700 p-2"
									inline
								/>
									<span class="flex items-center gap-2 text-sm">
										{#if platform.value !== 'All'}
											<span class={`flex items-center gap-2 px-2 py-1 text-xs`}>
												{#if platform.value === 'TELEPHONE'}
													<PhoneSolid class="h-5 w-5" />
												{:else if platform.value === 'LINE'}
													<img
														src="/images/platform-line.png"
														alt="LINE Icon"
														class="h-5 w-5 rounded-full"
													/>
												{:else if platform.value === 'FACEBOOK'}
													<img
														src="/images/platform-facebook.png"
														alt="Facebook Icon"
														class="h-5 w-5 rounded-full"
													/>
												{:else if platform.value === 'WHATSAPP'}
													<img
														src="/images/platform-whatsapp.png"
														alt="WhatsApp Icon"
														class="h-5 w-5 rounded-full"
													/>
												{:else if platform.value === 'INSTAGRAM'}
													<img
														src="/images/platform-instagram.png"
														alt="Instagram Icon"
														class="h-5 w-5 rounded-full"
													/>
												{:else if platform.value === 'TELEGRAM'}
													<img
														src="/images/platform-telegram.png"
														alt="Telegram Icon"
														class="h-5 w-5 rounded-full"
													/>
												{/if}
												{platform.label}
											</span>
										{:else if platform.value === 'All'}
											{t('filter_all')}
										{/if}
									</span>
							</Label>
						{/each}
					</Dropdown>
				</div>

				<!-- Reset Filter -->
				<Button
					id="customer-page-reset-filters-button"
					color="none"
					on:click={resetFilters}
					class="w-auto border shadow-md hover:bg-gray-100"
				>
					{t('filter_reset')}
				</Button>

				<!-- Loading indicator -->
				{#if isLoading}
					<div class="flex items-center gap-2">
						<span class="text-sm text-gray-500">{t('loading')}</span>
					</div>
				{/if}
			</div>

			<!-- Right side - Search Bar -->
			<div id="customer-page-search-container" class="relative w-full shadow-md lg:w-1/3">
				<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
					<SearchOutline class="h-5 w-5 text-gray-500" />
				</div>
				<Input
					id="customer-page-search-input"
					type="text"
					placeholder={t('customer_search_placeholder')}
					bind:value={searchQuery}
					class={`block w-full rounded-lg border bg-white py-2.5 pl-10
                        focus:border-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-700
                        ${searchQuery ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
				/>
			</div>
		</div>

		{#if error}
			<p class="text-red-600">{error}</p>
		{:else}
			<Table id="customer-page-table" sort={compare} hoverable shadow class="w-full table-fixed">
				<TableHead id="customer-page-table-head">
					<TableHeadCell id="customer-page-header-customer-id" class="w-[70px]" on:click={() => sortBy('customer_id')}>
						<div class="flex items-center">
							{t('table_no')}
							{#if sortColumn === 'customer_id'}
								{#if sortDirection === 'asc'}
									<CaretUpSolid class="ml-1 h-4 w-4" />
								{:else}
									<CaretDownSolid class="ml-1 h-4 w-4" />
								{/if}
							{/if}
						</div>
					</TableHeadCell>

					<TableHeadCell id="customer-page-header-name" class="w-[250px]" on:click={() => sortBy('name')}>
						<div class="flex items-center">
							{t('table_name')}
							{#if sortColumn === 'name'}
								{#if sortDirection === 'asc'}
									<CaretUpSolid class="ml-1 h-4 w-4" />
								{:else}
									<CaretDownSolid class="ml-1 h-4 w-4" />
								{/if}
							{/if}
						</div>
					</TableHeadCell>

					<TableHeadCell id="customer-page-header-tag" class="w-[150px]">
						{t('tag')}
					</TableHeadCell>

					<TableHeadCell id="customer-page-header-platforms" class="w-[150px]" on:click={() => sortBy('platforms')}>
						<div class="flex items-center">
							{t('table_platform')}
							{#if sortColumn === 'platforms'}
								{#if sortDirection === 'asc'}
									<CaretUpSolid class="ml-1 h-4 w-4" />
								{:else}
									<CaretDownSolid class="ml-1 h-4 w-4" />
								{/if}
							{/if}
						</div>
					</TableHeadCell>

					<TableHeadCell id="customer-page-header-phone" class="w-[180px]">{t('table_phone')}</TableHeadCell>

					<TableHeadCell id="customer-page-header-email" class="w-[280px]" on:click={() => sortBy('email')}>
						<div class="flex items-center">
							{t('table_email')}
							{#if sortColumn === 'email'}
								{#if sortDirection === 'asc'}
									<CaretUpSolid class="ml-1 h-4 w-4" />
								{:else}
									<CaretDownSolid class="ml-1 h-4 w-4" />
								{/if}
							{/if}
						</div>
					</TableHeadCell>

					<!-- <TableHeadCell class="w-[150px]" on:click={() => sortBy('created_on')}>{t('table_created_on')}</TableHeadCell> -->
				</TableHead>

				<TableBody id="customer-page-table-body">
					{#if paginatedCustomers.length === 0}
						<TableBodyRow id="customer-page-no-data-row">
							<TableBodyCell id="customer-page-no-data-cell" colspan={6} class="py-8 text-center">
								{#if searchQuery.trim() || !selectedTags.has('All') || !selectedPlatforms.has('All')}
									<div class="text-gray-500">
										<div class="mb-2 text-lg">🔍</div>
										<div class="font-medium text-gray-700">{t('customer_table_search_no_results')}</div>
									</div>
								{:else}
									<div class="text-gray-500">
										<div class="mb-2 text-lg">👥</div>
										<div class="font-medium text-gray-700">{t('table_no_data')}</div>
									</div>
								{/if}
							</TableBodyCell>
						</TableBodyRow>
					{:else}
						{#each paginatedCustomers as item}
							<TableBodyRow
								id="customer-page-row-{item.customer_id}"
								slot="row"
								class="cursor-pointer hover:bg-gray-50 {selectedCustomerId === item.customer_id ? 'bg-blue-50' : ''}"
								on:click={() => openSidebar(item.customer_id)}
							>
								<TableBodyCell id="customer-page-cell-id-{item.customer_id}" class="text-center">
									<!-- <div class="py-2 text-blue-600"> -->
										{item.customer_id}
									<!-- </div> -->
								</TableBodyCell>

								<TableBodyCell id="customer-page-cell-name-{item.customer_id}">
									<span class="break-words">
										<!-- {item.name ? item.name : item.line_user ? item.name : '-'} -->
										{item.first_name && item.last_name
											? `${item.first_name} ${item.last_name}`
											: item.name || '-'}
									</span>

									<!-- <div class="truncate text-xs text-gray-500">{item.name}</div> -->
								</TableBodyCell>

								<!-- Fixed Tag Display Section -->
								<TableBodyCell id="customer-page-cell-tags-{item.customer_id}">
									{#if item.tags && item.tags.length > 0}
										{#if item.tags.length === 1}
											<!-- Single tag display -->
											<span id="customer-page-tag-single-{item.customer_id}" class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
												<Indicator
													size="sm"
													class={`mr-1 ${getColorClass(item.tags[0].color)} inline-block`}
												/>
												{formatTagName(item.tags[0].name)}
											</span>
										{:else}
											<!-- Multiple tags display -->
											<div class="relative inline-block">
												<span
													class="inline-flex cursor-pointer items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm"
													data-popover-target="popover-tags-{item.customer_id}"
												>
													<span class="flex items-center gap-1">
														<span class="relative flex -space-x-1">
															{#each item.tags.slice(0, 3) as tag, i (tag.name)}
																<Indicator
																	size="sm"
																	class={`${getColorClass(tag.color)}`}
																	style="z-index: {10 - i};"
																/>
															{/each}
														</span>
														{item.tags.length}
														{t('labels')}
													</span>
												</span>

												<!-- Tooltip for all tags -->
												<Tooltip triggeredBy="[data-popover-target='popover-tags-{item.customer_id}']" class="tags-tooltip">
													<div class="max-w-xs px-2 py-1">
														<ul class="space-y-1">
															{#each item.tags as tag}
																<li class="flex items-center gap-1">
																	<Indicator
																		size="sm"
																		class={`mr-1 ${getColorClass(tag.color)}`}
																	/>
																	{formatTagName(tag.name)}
																</li>
															{/each}
														</ul>
													</div>
												</Tooltip>
											</div>
										{/if}
									{:else}
										<!-- No tags -->
										<!-- <span class="italic text-gray-500">{t('no_tags')}</span> -->
										-
									{/if}
								</TableBodyCell>
							
								<TableBodyCell id="customer-page-cell-platforms-{item.customer_id}">
									{#if item.platforms && item.platforms.length > 0}
										{@const platformGroups = item.platforms.reduce((acc, platform) => {
											if (!acc[platform.platform]) {
												acc[platform.platform] = [];
											}
											acc[platform.platform].push(platform.channel_name);
											return acc;
										}, {})}
										{@const uniquePlatforms = Object.keys(platformGroups)}
										
										<div
											class="inline-flex items-center justify-center gap-1 px-2 py-1 text-xs"
											id="customer-page-platforms-{item.customer_id}"
										>
											{#each uniquePlatforms as platformType}
												{#if platformType === 'LINE'}
													<img
														src="/images/platform-line.png"
														alt="LINE Icon"
														class="h-5 w-5"
													/>
												{:else if platformType === 'Facebook Messenger'}
													<img
														src="/images/platform-messenger.png"
														alt="Facebook Icon"
														class="h-5 w-5"
													/>
												<!-- {:else if platformType === 'Telephone'}
													<PhoneSolid class="h-5 w-5" /> -->
												{:else if platformType === 'WhatsApp'}
													<img
														src="/images/platform-whatsapp.png"
														alt="WhatsApp Icon"
														class="h-5 w-5"
													/>
												{:else if platformType === 'Instagram'}
													<img
														src="/images/platform-instagram.png"
														alt="Instagram Icon"
														class="h-5 w-5"
													/>
												{:else if platformType === 'Telegram'}
													<img
														src="/images/platform-telegram.png"
														alt="Telegram Icon"
														class="h-5 w-5"
													/>
												{/if}
											{/each}
										</div>
										
										<Tooltip id="customer-page-platforms-tooltip-{item.customer_id}" triggeredBy="#customer-page-platforms-{item.customer_id}" placement="top" class="platform-tooltip">
											<div class="space-y-2 max-w-xs">
												{#each Object.entries(platformGroups) as [platform, channels]}
													<div>
														<div class="font-medium text-sm">{platform}</div>
														<div class="text-sm space-y-1">
															{#each channels as channel}
																<div>{channel}</div>
															{/each}
														</div>
													</div>
												{/each}
											</div>
										</Tooltip>
									{:else}
										<div id="customer-page-no-platforms-{item.customer_id}" class="inline-flex items-center justify-center gap-2 rounded border px-2 py-1 text-xs">
											-
										</div>
									{/if}
								</TableBodyCell>

								<TableBodyCell id="customer-page-cell-phone-{item.customer_id}">{item.phone ? maskPhoneNumber(item.phone) : '-'}</TableBodyCell>

								<TableBodyCell id="customer-page-cell-email-{item.customer_id}">
									<span class="break-words">
										{item.email ?? '-'}
									</span>
								</TableBodyCell>
								<!-- <TableBodyCell>
                                    <div>{displayDate(item.created_on).date}</div>
                                    <div>{displayDate(item.created_on).time}</div>
                                </TableBodyCell> -->
							</TableBodyRow>
						{/each}
					{/if}
				</TableBody>
			</Table>

			<!-- Pagination Layout -->
			<Pagination id="customer-page-pagination" {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />
		{/if}
	</div>

	<!-- Customer Detail Sidebar -->
	<CustomerDetailSidebar
		bind:isOpen={isSidebarOpen}
		{selectedCustomerId}
		access_token={data.token || ''}
		on:closeSidebar={closeSidebar}
		on:loadingStateChanged={handleSidebarLoadingStateChanged}
	/>
</div>

<style>
	.break-words {
		word-break: break-word;
		white-space: normal;
	}

	/* Fix tooltip clipping issues in table containers */
	:global(.platform-tooltip),
	:global(.tags-tooltip) {
		z-index: 1000 !important;
		position: fixed !important;
	}

	/* Ensure tooltips are not clipped by overflow containers */
	:global(.platform-tooltip),
	:global(.tags-tooltip),
	:global(.platform-tooltip [data-popper-placement]),
	:global(.tags-tooltip [data-popper-placement]),
	:global(.platform-tooltip .tooltip-content),
	:global(.tags-tooltip .tooltip-content) {
		z-index: 1000 !important;
		position: fixed !important;
	}

	/* Override any overflow clipping for all tooltips in this page */
	:global(.tooltip),
	:global([role="tooltip"]),
	:global([data-popper-placement]) {
		z-index: 1000 !important;
		position: fixed !important;
	}

	/* Ensure tooltip positioning works correctly with table overflow */
	:global(.platform-tooltip[data-popper-placement]),
	:global(.tags-tooltip[data-popper-placement]) {
		position: fixed !important;
	}
</style>
