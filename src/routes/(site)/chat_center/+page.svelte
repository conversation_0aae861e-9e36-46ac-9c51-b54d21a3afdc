<!-- +page.svelte -->
<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import PlatformIdentityList from '$lib/components/chat/PlatformIdentityList.svelte';
	import ConversationView from '$lib/components/conversation/ConversationView.svelte';
	import CustomerInfoPanel from '$lib/components/customer/CustomerInfoPanel.svelte';
	import { platformWebSocket } from '$lib/websocket/platformWebSocket';
	import type { CustomerPlatformIdentity, Customer } from '$lib/types/customer';
	import { getBackendUrl } from '$src/lib/config';
	import { t } from '$lib/stores/i18n';

	export let data;

	let selectedPlatformId: number | null = null;
	let selectedCustomer: Customer | null = null;
	let selectedPlatformIdentity: CustomerPlatformIdentity | null = null;
	let latestTicketId: number | null = null;
	let loading = false;

	// Initialize with data from server
	let platformIdentities: CustomerPlatformIdentity[] = data.platformIdentities || [];
	let total = data.total || 0;
	let currentPage = data.page || 1;
	let hasMore = data.hasMore || false;
	let currentLoginUser = data.currentLoginUser || [];

	onMount(() => {
		// Connect WebSocket for real-time updates
		platformWebSocket.connect();
	});

	onDestroy(() => {
		platformWebSocket.disconnect();
	});

	async function handlePlatformSelect(
		event: CustomEvent<{
			platformId: number;
			customerId: number;
			platformIdentity: CustomerPlatformIdentity;
		}>
	) {
		const { platformId, customerId, platformIdentity } = event.detail;

		// Validate that we have both values
		if (!platformId || !customerId) {
			console.error('Missing platformId or customerId:', { platformId, customerId });
			return;
		}

		// Store the complete platform identity object
		selectedPlatformIdentity = platformIdentity;

		// Extract the latest ticket ID if available
		latestTicketId = platformIdentity.latest_ticket_id || null;

		// Update selected platform ID - this will trigger ConversationView to reload
		selectedPlatformId = platformId;

		// Load customer details if not already loaded or if different customer
		if (!selectedCustomer || selectedCustomer.customer_id !== customerId) {
			await loadCustomerDetails(customerId);
		}
	}

	async function loadCustomerDetails(customerId: number) {
		try {
			loading = true;
			const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/`, {
				credentials: 'include'
			});

			if (response.ok) {
				selectedCustomer = await response.json();
			}
			console.log('loadCustomerDetails: ', selectedCustomer);
		} catch (error) {
			console.error('Error loading customer:', error);
		} finally {
			loading = false;
		}
	}

	async function handleLoadMore(event: CustomEvent) {
		// Load more platform identities
		currentPage++;
		const response = await fetch(
			`${getBackendUrl()}/customer/api/platform-identities/?page=${currentPage}`,
			{ credentials: 'include' }
		);

		if (response.ok) {
			const data = await response.json();
			platformIdentities = [...platformIdentities, ...data.results];
			hasMore = !!data.next;
		}
	}

	// $: console.info('Selected data:', data);
	// $: console.info('Selected Platform ID:', selectedPlatformId);
	// $: console.info('Selected Customer:', selectedCustomer);
	// $: console.info('Platform Identities:', platformIdentities);

	// $: currentUserFullName = data.fullname || data.user?.full_name || '';
	// $: currentUserId = data.user_id || data.user?.id || '';
</script>

<div class="flex h-screen bg-gray-100">
	<!-- Left Panel: All Platform Identities -->
	<div class="flex w-1/4 min-w-[400px] flex-col border-r border-gray-200 bg-white">
		<PlatformIdentityList
			{platformIdentities}
			{selectedPlatformId}
			{hasMore}
			currentUserFullName={data.fullname}
			on:select={handlePlatformSelect}
			on:loadMore={handleLoadMore}
		/>
	</div>

	<!-- Middle Panel: Conversation -->
	<div class="flex flex-1 flex-col bg-white">
		{#if selectedPlatformId && selectedCustomer}
			<ConversationView
				customerId={selectedCustomer.customer_id}
				platformId={selectedPlatformId}
				ticketId={latestTicketId}
				users={data.allUsers}
				priorities={data.allPriorities}
				statuses={data.allStatuses}
				topics={data.allTopics}
				access_token={data.access_token}
				latest_ticket_owner_id={selectedPlatformIdentity.latest_ticket_owner_id}
				currentLoginUser={currentLoginUser}
			/>
		{:else}
			<div class="flex flex-1 items-center justify-center text-gray-500">
				<div class="text-center">
					<svg
						class="mx-auto mb-4 h-12 w-12 text-gray-400"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
						/>
					</svg>
					<p class="text-lg font-medium">{t('select_conversation')}</p>
					<p class="mt-1 text-sm text-gray-500">{t('choose_platform_identity')}</p>
				</div>
			</div>
		{/if}
	</div>

	<!-- Right Panel: Customer Info -->
	<!-- <div class="w-84 bg-white border-l border-gray-200"> -->
	<div class="flex w-1/4 min-w-[400px] flex-col overflow-hidden border-l border-gray-200 bg-white">
		{#if selectedPlatformId && selectedCustomer}
			<CustomerInfoPanel
				customer={selectedCustomer}
				platformId={selectedPlatformId}
				access_token={data.access_token}
			/>
		{:else}
			<div class="p-6 text-center text-gray-500">
				<p>{t('select_conversation_view_details')}</p>
			</div>
		{/if}
	</div>
</div>
