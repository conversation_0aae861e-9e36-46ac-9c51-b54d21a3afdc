/**
 * End-to-End Test: Account Settings Company Section CRUD Operations Workflow
 *
 * This comprehensive test validates the complete company settings management workflow
 * in the business settings page, specifically focusing on updating company information
 * using the unique HTML element IDs with "company-section-" prefix.
 *
 * SVELTEKIT PAGES TESTED:
 * - /settings/business (+page.svelte) - Business settings page with company configuration
 *   └── Loads company data via +page.server.ts load function
 *   └── Integrates CompanySection.svelte component for company settings management
 *   └── Page container: business-settings-page-container (line 249)
 *   └── Tab navigation: business-settings-tab-navigation (line 284)
 *   └── Company tab button: business-settings-company-tab (line 294)
 *   └── Company tab content: business-settings-company-tab-content (line 346)
 *
 * SVELTE COMPONENTS TESTED:
 * - CompanySection.svelte (/src/lib/components/settings/business/CompanySection.svelte)
 *   └── Thai name input: company-section-thai-name-input (line 185)
 *   └── English name input: company-section-english-name-input (line 202)
 *   └── Business input: company-section-business-input (line 219)
 *   └── Business type select: company-section-business-type-select (line 235)
 *   └── Save button: company-section-save-button (line 160)
 *   └── Unsaved changes warning: company-section-unsaved-changes-warning (line 252)
 *   └── Settings form: company-section-settings-form (line 279)
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to business settings page (/settings/business)
 * 2. Navigate to the 'company' tab to access company settings
 * 3. Capture original company information values for round-trip testing
 * 4. Update company settings (Thai name, English name, business, business type)
 * 5. Verify form button state management (disabled/enabled based on changes)
 * 6. Verify unsaved changes warning appears when form is modified
 * 7. Submit form and wait for successful completion
 * 8. Verify settings are saved and form state is reset
 * 9. Round-trip testing: revert changes to original values
 * 10. Verify restoration of original state and form functionality
 *
 * DATA FLOW AND INTEGRATION POINTS:
 * - SvelteKit server-side data loading and form actions
 * - CompanySection component reactive form state management
 * - Real-time UI updates based on form state changes
 * - Toast notification system for success/error feedback
 * - Form validation and change tracking functionality
 *
 * ROUND-TRIP TESTING PATTERN:
 * - Captures original company settings before any modifications
 * - Performs test operations and comprehensive verifications
 * - Reverts all changes to restore original state
 * - Ensures no test environment pollution
 * - Validates data persistence and form functionality
 *
 * ID SELECTOR STRATEGY:
 * All ID selectors use the "company-section-" prefix pattern established
 * in CompanySection.svelte component. Each selector references actual HTML elements
 * with documented line numbers for maintainability. Language-agnostic DOM
 * attribute assertions are used for robustness across different language settings.
 */

import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

// Import authentication utilities
import { performLoginWithRedirectHandling } from './utils/auth.utils';

/**
 * Utility function to navigate to business settings page
 * COMPONENT: Settings navigation and business page
 * ROUTE: /settings/business
 */
async function navigateToBusinessSettings(page: Page) {
	console.log('Navigating to business settings page...');

	// Navigate directly to business settings
	await page.goto('/settings/business');
	await page.waitForTimeout(1000);

	// Verify we're on the business settings page
	await expect(page).toHaveURL('/settings/business');

	// Wait for the main page container to load
	const pageContainer = page.locator('#business-settings-page-container');
	await expect(pageContainer).toBeVisible({ timeout: 10000 });

	// Wait for tab navigation to be ready
	const tabNavigation = page.locator('#business-settings-tab-navigation');
	await expect(tabNavigation).toBeVisible({ timeout: 10000 });

	console.log('✓ Successfully navigated to business settings page');
}

/**
 * Utility function to navigate to the company tab
 * COMPONENT: Business settings tab navigation
 */
async function navigateToCompanyTab(page: Page) {
	console.log('Navigating to company tab...');

	// Click on the company tab using the specific ID
	const companyTab = page.locator('#business-settings-company-tab');
	await expect(companyTab).toBeVisible({ timeout: 10000 });
	await companyTab.click();
	await page.waitForTimeout(1000);

	// Verify the company tab is selected
	await expect(companyTab).toHaveAttribute('aria-selected', 'true');

	// Wait for company tab content to be visible
	const companyTabContent = page.locator('#business-settings-company-tab-content');
	await expect(companyTabContent).toBeVisible({ timeout: 10000 });

	// Wait for CompanySection component to load
	const companySaveButton = page.locator('#company-section-save-button');
	await expect(companySaveButton).toBeVisible({ timeout: 10000 });

	console.log('✓ Successfully navigated to company tab');
}

/**
 * Utility function to verify tab activation and content visibility
 * COMPONENT: Business settings tab navigation and content
 */
async function verifyTabActivation(page: Page, tabName: string) {
	console.log(`Verifying ${tabName} tab activation...`);

	const tabButton = page.locator(`#business-settings-${tabName}-tab`);
	const tabContent = page.locator(`#business-settings-${tabName}-tab-content`);

	// Verify tab button is selected
	await expect(tabButton).toHaveAttribute('aria-selected', 'true');

	// Verify tab content is visible
	await expect(tabContent).toBeVisible({ timeout: 5000 });

	// Verify other tabs are not selected
	const allTabs = ['system', 'company', 'connection', 'bot'];
	for (const otherTab of allTabs) {
		if (otherTab !== tabName) {
			const otherTabButton = page.locator(`#business-settings-${otherTab}-tab`);
			await expect(otherTabButton).toHaveAttribute('aria-selected', 'false');
		}
	}

	console.log(`✓ ${tabName} tab activation verified`);
}

/**
 * Utility function to capture original company settings values
 * COMPONENT: CompanySection.svelte form inputs
 */
async function captureOriginalCompanyValues(page: Page) {
	console.log('Capturing original company settings values...');
	
	const thaiName = await page.locator('#company-section-thai-name-input').inputValue();
	const englishName = await page.locator('#company-section-english-name-input').inputValue();
	const business = await page.locator('#company-section-business-input').inputValue();
	const businessType = await page.locator('#company-section-business-type-select').inputValue();
	
	console.log('✓ Original company values captured:', {
		thaiName,
		englishName,
		business,
		businessType
	});
	
	return {
		thaiName,
		englishName,
		business,
		businessType
	};
}

/**
 * Utility function to update company settings with test values
 * COMPONENT: CompanySection.svelte form inputs
 */
async function updateCompanySettings(page: Page, testValues: any) {
	console.log('Updating company settings with test values...');
	
	// Update Thai name
	await page.locator('#company-section-thai-name-input').fill(testValues.thaiName);
	await page.waitForTimeout(500);
	
	// Update English name
	await page.locator('#company-section-english-name-input').fill(testValues.englishName);
	await page.waitForTimeout(500);
	
	// Update business
	await page.locator('#company-section-business-input').fill(testValues.business);
	await page.waitForTimeout(500);
	
	// Update business type
	await page.locator('#company-section-business-type-select').selectOption(testValues.businessType);
	await page.waitForTimeout(500);
	
	console.log('✓ Company settings updated with test values');
}

/**
 * Utility function to verify unsaved changes warning appears
 * COMPONENT: CompanySection.svelte unsaved changes warning
 */
async function verifyUnsavedChangesWarning(page: Page) {
	console.log('Verifying unsaved changes warning appears...');
	
	const warningElement = page.locator('#company-section-unsaved-changes-warning');
	await expect(warningElement).toBeVisible({ timeout: 5000 });
	
	console.log('✓ Unsaved changes warning is visible');
}

/**
 * Utility function to verify save button state
 * COMPONENT: CompanySection.svelte save button
 */
async function verifySaveButtonEnabled(page: Page) {
	console.log('Verifying save button is enabled...');
	
	const saveButton = page.locator('#company-section-save-button');
	await expect(saveButton).not.toBeDisabled({ timeout: 5000 });
	
	console.log('✓ Save button is enabled');
}

/**
 * Utility function to save company settings
 * COMPONENT: CompanySection.svelte save functionality
 */
async function saveCompanySettings(page: Page) {
	console.log('Saving company settings...');
	
	const saveButton = page.locator('#company-section-save-button');
	await saveButton.click();
	
	// Wait for save operation to complete (toast notification or form reset)
	await page.waitForTimeout(3000);
	
	// Verify save button is disabled after successful save
	await expect(saveButton).toBeDisabled({ timeout: 5000 });
	
	console.log('✓ Company settings saved successfully');
}

/**
 * Utility function to verify company settings values
 * COMPONENT: CompanySection.svelte form inputs
 */
async function verifyCompanyValues(page: Page, expectedValues: any) {
	console.log('Verifying company settings values...');
	
	await expect(page.locator('#company-section-thai-name-input')).toHaveValue(expectedValues.thaiName);
	await expect(page.locator('#company-section-english-name-input')).toHaveValue(expectedValues.englishName);
	await expect(page.locator('#company-section-business-input')).toHaveValue(expectedValues.business);
	await expect(page.locator('#company-section-business-type-select')).toHaveValue(expectedValues.businessType);
	
	console.log('✓ Company settings values verified');
}

test.describe('Account Settings Company Section CRUD Operations', () => {
	test('should complete full company settings CRUD workflow with round-trip testing', async ({ page }) => {
		// Step 1: Authentication and navigation
		await performLoginWithRedirectHandling(page);
		await navigateToBusinessSettings(page);
		await navigateToCompanyTab(page);

		// Step 1.5: Verify tab activation and content visibility
		await verifyTabActivation(page, 'company');

		// Step 2: Capture original values for round-trip testing
		const originalValues = await captureOriginalCompanyValues(page);
		
		// Step 3: Generate unique test values
		const testValues = {
			thaiName: `บริษัททดสอบ${Date.now()}`,
			englishName: `TestCompany${Date.now()}`,
			business: `Test Business ${Date.now()}`,
			businessType: 'Insurance'
		};
		
		// Step 4: Update company settings with test values
		await updateCompanySettings(page, testValues);
		
		// Step 5: Verify form state changes
		await verifyUnsavedChangesWarning(page);
		await verifySaveButtonEnabled(page);
		
		// Step 6: Save company settings
		await saveCompanySettings(page);
		
		// Step 7: Verify settings were saved
		await verifyCompanyValues(page, testValues);
		
		// Step 8: Round-trip testing - revert to original values
		console.log('Starting round-trip testing - reverting to original values...');
		await updateCompanySettings(page, originalValues);
		await saveCompanySettings(page);
		
		// Step 9: Verify restoration to original state
		await verifyCompanyValues(page, originalValues);
		
		console.log('✓ Company section CRUD workflow completed successfully with round-trip testing');
	});
});
