/**
 * End-to-End Test: Settings Team User Tag CRUD Operations Workflow
 *
 * This comprehensive test validates the complete user tag management CRUD operations
 * in the settings team page, specifically focusing on creating, editing, and deleting
 * user tags using the UserTag.svelte component with proper round-trip testing.
 *
 * SVELTEKIT PAGES TESTED:
 * - /settings/team (+page.svelte) - Main settings team interface with user tag management
 *   └── Loads user tag data via +page.server.ts load function
 *   └── Integrates UserTag component for user tag CRUD operations
 *
 * SVELTE COMPONENTS TESTED:
 * - UserTag.svelte (/src/lib/components/settings/business/UserTag.svelte)
 *   └── Contains user tag list display and management functionality (lines 123-356)
 *   └── Add button: settings-team-user-tag-add-button (line 295)
 *   └── New form: settings-team-user-tag-new-form (line 312)
 *   └── New name input: settings-team-user-tag-new-name (line 321)
 *   └── New color picker: settings-team-user-tag-new-color-picker (line 302)
 *   └── New save button: settings-team-user-tag-new-save (line 342)
 *   └── New cancel button: settings-team-user-tag-new-cancel (line 349)
 *   └── Tag items (edit mode): settings-team-user-tag-item-{tag.id} (line 129)
 *   └── Tag items (display mode): settings-team-user-tag-display-{tag.id} (line 200)
 *   └── Tag name display: settings-team-user-tag-name-{tag.id} (line 207)
 *   └── Edit buttons: settings-team-user-tag-edit-button-{tag.id} (line 237)
 *   └── Edit forms: settings-team-user-tag-edit-form-{tag.id} (line 158)
 *   └── Edit name inputs: settings-team-user-tag-edit-name-{tag.id} (line 167)
 *   └── Edit color pickers: settings-team-user-tag-edit-color-picker-{tag.id} (line 132)
 *   └── Edit save buttons: settings-team-user-tag-edit-save-{tag.id} (line 179)
 *   └── Edit cancel buttons: settings-team-user-tag-edit-cancel-{tag.id} (line 188)
 *   └── Delete buttons: settings-team-user-tag-delete-button-{tag.id} (line 244)
 *   └── Delete confirm buttons: settings-team-user-tag-delete-confirm-{tag.id} (line 220)
 *   └── Delete cancel buttons: settings-team-user-tag-delete-cancel-{tag.id} (line 227)
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to settings team page (/settings/team)
 * 2. User tag creation with name and color selection
 * 3. User tag editing with name and color modifications
 * 4. User tag deletion with confirmation workflow
 * 5. Round-trip testing to restore original state and avoid test pollution
 * 6. Form validation and error handling verification
 * 7. Data persistence verification across page operations
 *
 * ROUND-TRIP TESTING PATTERN:
 * - Capture original user tag data before any modifications
 * - Perform test operations (create, edit, delete)
 * - Verify expected changes and functionality
 * - Restore original state by reverting all changes
 * - Verify restoration to prevent test environment pollution
 */

import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

// Import authentication utilities
import { performLoginWithRedirectHandling } from './utils/auth.utils';

// Utility function to navigate to settings team page
async function navigateToTeamSettings(page: Page) {
	console.log('Navigating to settings team page...');
	await page.goto('/settings/team');
	await expect(page).toHaveURL('/settings/team');
	await page.waitForTimeout(2000); // Allow page to load
	console.log('✓ Successfully navigated to settings team page');
}

// Utility function to open the user tags accordion section
async function openUserTagsAccordion(page: Page) {
	console.log('Opening user tags accordion section...');
	const accordionTrigger = page.locator('#settings-team-user-tags-accordion-trigger');

	// Check if accordion is already open by looking for visible content
	const addButton = page.locator('#settings-team-user-tag-add-button');
	const isOpen = await addButton.isVisible().catch(() => false);

	if (!isOpen) {
		await expect(accordionTrigger).toBeVisible({ timeout: 10000 });
		await accordionTrigger.click();
		await page.waitForTimeout(1000); // Wait for accordion animation

		// Verify accordion opened
		await expect(addButton).toBeVisible({ timeout: 5000 });
	}

	console.log('✓ User tags accordion section opened');
}

// Utility function to capture original user tag data for round-trip testing
async function captureOriginalUserTagData(page: Page) {
	console.log('Capturing original user tag data...');
	const tags = [];
	
	// Wait for tag list to load
	await page.waitForTimeout(1000);
	
	// Find all tag items (both display and edit modes)
	const displayTagItems = await page.locator('[id^="settings-team-user-tag-display-"]').all();
	const editTagItems = await page.locator('[id^="settings-team-user-tag-item-"]').all();
	
	// Combine both types of tag items
	const allTagItems = [...displayTagItems, ...editTagItems];
	
	for (const item of allTagItems) {
		const itemId = await item.getAttribute('id');
		const tagId = itemId?.split('-').pop();
		
		if (tagId) {
			const nameElement = page.locator(`#settings-team-user-tag-name-${tagId}`);
			
			if (await nameElement.isVisible()) {
				const name = await nameElement.textContent();
				
				tags.push({
					id: tagId,
					name: name?.trim() || ''
				});
			}
		}
	}
	
	console.log(`✓ Captured ${tags.length} original user tags`);
	return tags;
}

// Utility function to open add user tag form
async function openAddUserTagForm(page: Page) {
	console.log('Opening add user tag form...');
	const addButton = page.locator('#settings-team-user-tag-add-button');
	await expect(addButton).toBeVisible({ timeout: 10000 });
	await addButton.click();
	await page.waitForTimeout(1000);
	
	// Verify form is visible
	const newForm = page.locator('#settings-team-user-tag-new-form');
	await expect(newForm).toBeVisible({ timeout: 5000 });
	console.log('✓ Add user tag form opened');
}

// Utility function to create a new user tag
async function createNewUserTag(page: Page, name: string) {
	console.log(`Creating new user tag: ${name}`);
	
	// Fill in the form fields
	const nameInput = page.locator('#settings-team-user-tag-new-name');
	
	await expect(nameInput).toBeVisible();
	await nameInput.fill(name);
	
	// Submit the form
	const saveButton = page.locator('#settings-team-user-tag-new-save');
	await expect(saveButton).toBeVisible();
	await expect(saveButton).toBeEnabled();
	await saveButton.click();
	
	// Wait for form submission and page update
	await page.waitForTimeout(2000);
	console.log(`✓ User tag ${name} created successfully`);
}

// Utility function to edit an existing user tag
async function editUserTag(page: Page, tagId: string, newName: string) {
	console.log(`Editing user tag ${tagId}: ${newName}`);
	
	// Click edit button
	const editButton = page.locator(`#settings-team-user-tag-edit-button-${tagId}`);
	await expect(editButton).toBeVisible();
	await editButton.click();
	await page.waitForTimeout(1000);
	
	// Verify edit form is visible
	const editForm = page.locator(`#settings-team-user-tag-edit-form-${tagId}`);
	await expect(editForm).toBeVisible();
	
	// Fill in the edit fields
	const nameInput = page.locator(`#settings-team-user-tag-edit-name-${tagId}`);
	
	await expect(nameInput).toBeVisible();
	await nameInput.clear();
	await nameInput.fill(newName);
	
	// Submit the edit
	const saveButton = page.locator(`#settings-team-user-tag-edit-save-${tagId}`);
	await expect(saveButton).toBeVisible();
	await expect(saveButton).toBeEnabled();
	await saveButton.click();
	
	// Wait for form submission
	await page.waitForTimeout(2000);
	console.log(`✓ User tag ${tagId} edited successfully`);
}

// Utility function to delete a user tag
async function deleteUserTag(page: Page, tagId: string) {
	console.log(`Deleting user tag ${tagId}`);
	
	// Click delete button
	const deleteButton = page.locator(`#settings-team-user-tag-delete-button-${tagId}`);
	await expect(deleteButton).toBeVisible();
	await deleteButton.click();
	await page.waitForTimeout(1000);
	
	// Confirm deletion
	const confirmButton = page.locator(`#settings-team-user-tag-delete-confirm-${tagId}`);
	await expect(confirmButton).toBeVisible();
	await confirmButton.click();
	
	// Wait for deletion to complete
	await page.waitForTimeout(2000);
	console.log(`✓ User tag ${tagId} deleted successfully`);
}

test.describe('Settings Team User Tag CRUD Operations', () => {
	test('should complete full user tag CRUD workflow with round-trip testing', async ({ page }) => {
		// Step 1: Authentication and navigation
		await performLoginWithRedirectHandling(page);
		await navigateToTeamSettings(page);

		// Step 2: Open user tags accordion section
		await openUserTagsAccordion(page);

		// Step 3: Capture original state for round-trip testing
		const originalTags = await captureOriginalUserTagData(page);
		
		// Step 3: Test user tag creation
		await openAddUserTagForm(page);
		
		const testTagName = `TestUserTag${Date.now()}`;
		
		await createNewUserTag(page, testTagName);
		
		// Verify user tag was created
		const createdTagName = page.locator(`text=${testTagName}`);
		await expect(createdTagName).toBeVisible({ timeout: 10000 });
		console.log('✓ User tag creation verified');
		
		// Step 4: Test user tag editing
		// Find the created tag's ID
		const displayTagItems = await page.locator('[id^="settings-team-user-tag-display-"]').all();
		let createdTagId = null;
		
		for (const item of displayTagItems) {
			const nameElement = item.locator('[id^="settings-team-user-tag-name-"]');
			if (await nameElement.isVisible()) {
				const name = await nameElement.textContent();
				if (name?.includes(testTagName)) {
					const itemId = await item.getAttribute('id');
					createdTagId = itemId?.split('-').pop();
					break;
				}
			}
		}
		
		if (createdTagId) {
			const editedName = `${testTagName}_Edited`;
			
			await editUserTag(page, createdTagId, editedName);
			
			// Verify user tag was edited
			const editedTagName = page.locator(`text=${editedName}`);
			await expect(editedTagName).toBeVisible({ timeout: 10000 });
			console.log('✓ User tag editing verified');
			
			// Step 5: Test user tag deletion
			await deleteUserTag(page, createdTagId);
			
			// Verify user tag was deleted
			await expect(editedTagName).not.toBeVisible({ timeout: 10000 });
			console.log('✓ User tag deletion verified');
		}
		
		// Step 6: Verify round-trip - original state should be restored
		const finalTags = await captureOriginalUserTagData(page);
		expect(finalTags.length).toBe(originalTags.length);
		console.log('✓ Round-trip testing completed - original state restored');
		
		console.log('🎉 User tag CRUD workflow with round-trip testing completed successfully!');
	});
});
