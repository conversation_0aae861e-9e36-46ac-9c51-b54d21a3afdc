/**
 * End-to-End Test: Settings Team Customer Tag CRUD Operations Workflow
 *
 * This comprehensive test validates the complete customer tag management CRUD operations
 * in the settings team page, specifically focusing on creating, editing, and deleting
 * customer tags using the CustomerTag.svelte component with proper round-trip testing.
 *
 * SVELTEKIT PAGES TESTED:
 * - /settings/team (+page.svelte) - Main settings team interface with customer tag management
 *   └── Loads customer tag data via +page.server.ts load function
 *   └── Integrates CustomerTag component for customer tag CRUD operations
 *
 * SVELTE COMPONENTS TESTED:
 * - CustomerTag.svelte (/src/lib/components/settings/business/CustomerTag.svelte)
 *   └── Contains customer tag list display and management functionality (lines 120-363)
 *   └── Add button: settings-team-customer-tag-add-button (line 295)
 *   └── New form: settings-team-customer-tag-new-form (line 312)
 *   └── New name input: settings-team-customer-tag-new-name (line 321)
 *   └── New color picker: settings-team-customer-tag-new-color-picker (line 302)
 *   └── New save button: settings-team-customer-tag-new-save (line 342)
 *   └── New cancel button: settings-team-customer-tag-new-cancel (line 349)
 *   └── Tag items: settings-team-customer-tag-item-{tag.id} (line 130)
 *   └── Tag info: settings-team-customer-tag-info-{tag.id} (line 203)
 *   └── Tag name display: settings-team-customer-tag-name-{tag.id} (line 207)
 *   └── Edit buttons: settings-team-customer-tag-edit-button-{tag.id} (line 244)
 *   └── Edit forms: settings-team-customer-tag-edit-form-{tag.id} (line 158)
 *   └── Edit name inputs: settings-team-customer-tag-edit-name-{tag.id} (line 167)
 *   └── Edit color pickers: settings-team-customer-tag-edit-color-picker-{tag.id} (line 134)
 *   └── Edit save buttons: settings-team-customer-tag-edit-save-{tag.id} (line 184)
 *   └── Edit cancel buttons: settings-team-customer-tag-edit-cancel-{tag.id} (line 193)
 *   └── Delete buttons: settings-team-customer-tag-delete-button-{tag.id} (line 252)
 *   └── Delete confirm buttons: settings-team-customer-tag-delete-confirm-{tag.id} (line 264)
 *   └── Delete cancel buttons: settings-team-customer-tag-delete-cancel-{tag.id} (line 271)
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to settings team page (/settings/team)
 * 2. Customer tag creation with name and color selection
 * 3. Customer tag editing with name and color modifications
 * 4. Customer tag deletion with confirmation workflow
 * 5. Round-trip testing to restore original state and avoid test pollution
 * 6. Form validation and error handling verification
 * 7. Data persistence verification across page operations
 *
 * ROUND-TRIP TESTING PATTERN:
 * - Capture original customer tag data before any modifications
 * - Perform test operations (create, edit, delete)
 * - Verify expected changes and functionality
 * - Restore original state by reverting all changes
 * - Verify restoration to prevent test environment pollution
 */

import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

// Import authentication utilities
import { performLoginWithRedirectHandling } from './utils/auth.utils';

// Utility function to navigate to settings team page
async function navigateToTeamSettings(page: Page) {
	console.log('Navigating to settings team page...');
	await page.goto('/settings/team');
	await expect(page).toHaveURL('/settings/team');
	await page.waitForTimeout(2000); // Allow page to load
	console.log('✓ Successfully navigated to settings team page');
}

// Utility function to navigate to customer tab and open customer tags accordion
async function navigateToCustomerTabAndOpenAccordion(page: Page) {
	console.log('Navigating to customer tab...');

	// Click on the customer tab
	const customerTab = page.locator('#settings-team-customer-tab');
	await expect(customerTab).toBeVisible({ timeout: 10000 });
	await customerTab.click();
	await page.waitForTimeout(1000);

	console.log('Opening customer tags accordion section...');
	const accordionTrigger = page.locator('#settings-team-customer-tags-accordion-trigger');

	// Check if accordion is already open by looking for visible content
	const addButton = page.locator('#settings-team-customer-tag-add-button');
	const isOpen = await addButton.isVisible().catch(() => false);

	if (!isOpen) {
		await expect(accordionTrigger).toBeVisible({ timeout: 10000 });
		await accordionTrigger.click();
		await page.waitForTimeout(1000); // Wait for accordion animation

		// Verify accordion opened
		await expect(addButton).toBeVisible({ timeout: 5000 });
	}

	console.log('✓ Customer tags accordion section opened');
}

// Utility function to capture original customer tag data for round-trip testing
async function captureOriginalCustomerTagData(page: Page) {
	console.log('Capturing original customer tag data...');
	const tags = [];
	
	// Wait for tag list to load
	await page.waitForTimeout(1000);
	
	// Find all customer tag items
	const tagItems = await page.locator('[id^="settings-team-customer-tag-item-"]').all();
	
	for (const item of tagItems) {
		const itemId = await item.getAttribute('id');
		const tagId = itemId?.split('-').pop();
		
		if (tagId) {
			const nameElement = page.locator(`#settings-team-customer-tag-name-${tagId}`);
			
			if (await nameElement.isVisible()) {
				const name = await nameElement.textContent();
				
				tags.push({
					id: tagId,
					name: name?.trim() || ''
				});
			}
		}
	}
	
	console.log(`✓ Captured ${tags.length} original customer tags`);
	return tags;
}

// Utility function to open add customer tag form
async function openAddCustomerTagForm(page: Page) {
	console.log('Opening add customer tag form...');
	const addButton = page.locator('#settings-team-customer-tag-add-button');
	await expect(addButton).toBeVisible({ timeout: 10000 });
	await addButton.click();
	await page.waitForTimeout(1000);
	
	// Verify form is visible
	const newForm = page.locator('#settings-team-customer-tag-new-form');
	await expect(newForm).toBeVisible({ timeout: 5000 });
	console.log('✓ Add customer tag form opened');
}

// Utility function to create a new customer tag
async function createNewCustomerTag(page: Page, name: string) {
	console.log(`Creating new customer tag: ${name}`);
	
	// Fill in the form fields
	const nameInput = page.locator('#settings-team-customer-tag-new-name');
	
	await expect(nameInput).toBeVisible();
	await nameInput.fill(name);
	
	// Submit the form
	const saveButton = page.locator('#settings-team-customer-tag-new-save');
	await expect(saveButton).toBeVisible();
	await expect(saveButton).toBeEnabled();
	await saveButton.click();
	
	// Wait for form submission and page update
	await page.waitForTimeout(2000);
	console.log(`✓ Customer tag ${name} created successfully`);
}

// Utility function to edit an existing customer tag
async function editCustomerTag(page: Page, tagId: string, newName: string) {
	console.log(`Editing customer tag ${tagId}: ${newName}`);
	
	// Click edit button
	const editButton = page.locator(`#settings-team-customer-tag-edit-button-${tagId}`);
	await expect(editButton).toBeVisible();
	await editButton.click();
	await page.waitForTimeout(1000);
	
	// Verify edit form is visible
	const editForm = page.locator(`#settings-team-customer-tag-edit-form-${tagId}`);
	await expect(editForm).toBeVisible();
	
	// Fill in the edit fields
	const nameInput = page.locator(`#settings-team-customer-tag-edit-name-${tagId}`);
	
	await expect(nameInput).toBeVisible();
	await nameInput.clear();
	await nameInput.fill(newName);
	
	// Submit the edit
	const saveButton = page.locator(`#settings-team-customer-tag-edit-save-${tagId}`);
	await expect(saveButton).toBeVisible();
	await expect(saveButton).toBeEnabled();
	await saveButton.click();
	
	// Wait for form submission
	await page.waitForTimeout(2000);
	console.log(`✓ Customer tag ${tagId} edited successfully`);
}

// Utility function to delete a customer tag
async function deleteCustomerTag(page: Page, tagId: string) {
	console.log(`Deleting customer tag ${tagId}`);
	
	// Click delete button
	const deleteButton = page.locator(`#settings-team-customer-tag-delete-button-${tagId}`);
	await expect(deleteButton).toBeVisible();
	await deleteButton.click();
	await page.waitForTimeout(1000);
	
	// Confirm deletion
	const confirmButton = page.locator(`#settings-team-customer-tag-delete-confirm-${tagId}`);
	await expect(confirmButton).toBeVisible();
	await confirmButton.click();
	
	// Wait for deletion to complete
	await page.waitForTimeout(2000);
	console.log(`✓ Customer tag ${tagId} deleted successfully`);
}

test.describe('Settings Team Customer Tag CRUD Operations', () => {
	test('should complete full customer tag CRUD workflow with round-trip testing', async ({ page }) => {
		// Step 1: Authentication and navigation
		await performLoginWithRedirectHandling(page);
		await navigateToTeamSettings(page);

		// Step 2: Navigate to customer tab and open accordion
		await navigateToCustomerTabAndOpenAccordion(page);

		// Step 3: Capture original state for round-trip testing
		const originalTags = await captureOriginalCustomerTagData(page);
		
		// Step 3: Test customer tag creation
		await openAddCustomerTagForm(page);
		
		const testTagName = `TestCustomerTag${Date.now()}`;
		
		await createNewCustomerTag(page, testTagName);
		
		// Verify customer tag was created
		const createdTagName = page.locator(`text=${testTagName}`);
		await expect(createdTagName).toBeVisible({ timeout: 10000 });
		console.log('✓ Customer tag creation verified');
		
		// Step 4: Test customer tag editing
		// Find the created tag's ID
		const tagItems = await page.locator('[id^="settings-team-customer-tag-item-"]').all();
		let createdTagId = null;
		
		for (const item of tagItems) {
			const nameElement = item.locator('[id^="settings-team-customer-tag-name-"]');
			if (await nameElement.isVisible()) {
				const name = await nameElement.textContent();
				if (name?.includes(testTagName)) {
					const itemId = await item.getAttribute('id');
					createdTagId = itemId?.split('-').pop();
					break;
				}
			}
		}
		
		if (createdTagId) {
			const editedName = `${testTagName}_Edited`;
			
			await editCustomerTag(page, createdTagId, editedName);
			
			// Verify customer tag was edited
			const editedTagName = page.locator(`text=${editedName}`);
			await expect(editedTagName).toBeVisible({ timeout: 10000 });
			console.log('✓ Customer tag editing verified');
			
			// Step 5: Test customer tag deletion
			await deleteCustomerTag(page, createdTagId);
			
			// Verify customer tag was deleted
			await expect(editedTagName).not.toBeVisible({ timeout: 10000 });
			console.log('✓ Customer tag deletion verified');
		}
		
		// Step 6: Verify round-trip - original state should be restored
		const finalTags = await captureOriginalCustomerTagData(page);
		expect(finalTags.length).toBe(originalTags.length);
		console.log('✓ Round-trip testing completed - original state restored');
		
		console.log('🎉 Customer tag CRUD workflow with round-trip testing completed successfully!');
	});
});
